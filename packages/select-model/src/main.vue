<template>
  <yxt-drawer ref="drawer" :title="$t('pc_biz_lbl_selectModel').d('选取模型')" :visible.sync="visible" size="960px"
    destroy-on-close :before-close="handleBeforeClose" class="yxtbiz-select-model" @close="closeDrawer">
    <div class="yxtbiz-select-model__body">
      <div class="more_position" :class="{ 'more_position_width': limit !== 1 }">
        <div class="table_wrapper">
          <div class="table_wrapper_search mb20">
            <yxtbiz-category-selector v-model="catalog" code="1078258112543268865" apiName="sptalentsd"
              :dropdownWidth="600" :type="1" class="w200 mr12" @change="search">
            </yxtbiz-category-selector>
            <yxt-select v-model="params.sceneDictIds" clearable multiple collapse-tags
              :placeholder="$t('pc_biz_lbl_scene').d('应用场景')" class="w144 mr12" @change="search">
              <yxt-option v-for="item in sceneDict" :key="item.id" :label="item.dictName" :value="item.id">
              </yxt-option>
            </yxt-select>
            <yxt-select v-model="params.applyDictIds" clearable multiple collapse-tags
              :placeholder="$t('pc_biz_lbl_apply').d('适用人群')" class="w144 mr12" @change="search">
              <yxt-option v-for="item in applyDict" :key="item.id" :label="item.dictName" :value="item.id">
              </yxt-option>
            </yxt-select>
            <yxt-input v-model="params.keyword" class="w220 keyword"
              :placeholder="$t('pc_gwnl_model_template__info_basic_namePlaceHolder').d('请输入模型名称')" searchable clearable
              @search="search" />
          </div>
          <yxt-table ref="table" v-loading="loading" class="person-table width-percent-100" row-key="id"
            :data="tableData" :height="tableData.lenght > 20 ? 668 : 'auto'" @radio-change="handleRaidoSelect"
            @select="handleSingleSelect" @select-all="handleSelectAll">
            <yxt-table-column :type="limit === 1 ? 'radio' : 'selection'" width="55" />
            <yxt-table-column prop="title" show-overflow-tooltip :label="$t('pc_gwnl_model_name').d('模型名称')"
              min-width="180">
              <template slot-scope="{row}">
                <yxt-link type="primary" @click="goDetails(row)">{{ row.title || '--' }}</yxt-link>
              </template>
            </yxt-table-column>
            <yxt-table-column prop="catalogName" show-overflow-tooltip :label="$t('pc_gwnl_model_catalogs').d('模型分类')">
              <template slot-scope="{row}">
                {{ row.catalogName || '--' }}
              </template>
            </yxt-table-column>
            <yxt-table-column show-overflow-tooltip :label="$t('pc_biz_lbl_scene').d('应用场景')">
              <template slot-scope="{row}">
                {{ filterData(row.scenes) }}
              </template>
            </yxt-table-column>
            <yxt-table-column show-overflow-tooltip :label="$t('pc_biz_lbl_apply').d('适用人群')">
              <template slot-scope="{row}">
                {{ filterData(row.applys) }}
              </template>
            </yxt-table-column>
            <template slot="empty">
              <div style="line-height: normal;">
                <yxt-empty />
              </div>
            </template>
          </yxt-table>
          <pager :total="total" :page.sync="pageParams.offset" :limit.sync="pageParams.limit"
            @pagination="getModelData" />
        </div>
      </div>
      <div v-if="limit !== 1" class="yxt-list__wrapper yxt-list__wrapper-single">
        <check-list :list="checkedList" @clear="clear" @close="closeTag" />
      </div>
    </div>
    <div slot="footer">
      <yxt-button @click="() => $refs.drawer.closeDrawer()">
        {{ $t('pc_biz_common_btn_cancel').d('取消') }}
      </yxt-button>
      <yxt-button :disabled="loading" type="primary" @click="confirm">
        {{ $t('pc_gwnl_global_btn_confirm').d('确认') }}
      </yxt-button>
    </div>
  </yxt-drawer>
</template>

<script>
import checkList from './components/check-list';
import pager from './components/pager.vue';
import { getModelList, getDictList } from './service';
import mixins from './mixins';

export default {
  name: 'YxtbizSelectModel',
  mixins: [mixins],
  components: {
    checkList,
    pager
  },
  props: {
    selected: {
      type: Array,
      default: () => []
    },
    navCode: {
      type: String,
      default: ''
    },
    dataPermsCode: {
      type: String,
      default: ''
    },
    modelType: {
      type: Number, // 0：所有类型    1：能力模型   2：技能模型   3：任务模型   4：知识模型  5：复合模型
      default: 0
    },
    levelType: { // 0-无类型 1-有等级模型 2-无等级模型
      type: Number,
      default: 0
    },
    limit: {
      type: Number,
      default: Infinity
    },
    manualClose: { // 是否手动关闭抽屉
      type: Boolean,
      default: false
    },
    modelIds: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      visible: false,
      params: {
        sceneDictIds: [], // 场景ID
        applyDictIds: [], // 适用人群id
        keyword: '' // 搜索关键字
      },
      pageParams: {
        limit: 20,
        offset: 0
      },
      loading: false,
      total: 0,
      tableData: [],
      sceneDict: [],
      applyDict: [],
      catalog: {}
    };
  },
  methods: {
    handleBeforeClose(done) {
      const isDiff = JSON.stringify(this.selected) !== JSON.stringify(this.checkedList);
      done(isDiff);
    },
    open() {
      this.checkedList = [...this.selected];
      this.getDicts(9);
      this.getDicts(8);
      this.search();
      this.visible = true;
    },
    closeDrawer() {
      this.visible = false;
      this.clear();
    },
    confirm() {
      if (this.limit && this.checkedList.length > this.limit) {
        this.$message.warning(this.$t('pc_designer_sp_lbl_selectMaxLimit', { limit: this.limit }).d(`最多可选择${this.limit}个`));
      } else {
        this.$emit('confirm', this.checkedList);
        !this.manualClose && this.closeDrawer();
      }
    },
    getModelData() {
      if (this.loading) return;
      this.loading = true;
      let params = {
        ...this.params,
        catalogId: this.catalog.nodeId || '',
        levelType: !this.levelType ? null : this.levelType,
        modelType: !this.modelType ? '' : this.modelType,
        navCode: this.navCode,
        dataScope: this.dataPermsCode,
        modelIds: this.modelIds || []
      };
      getModelList(params, this.pageParams).then(res => {
        this.tableData = res.datas;
        this.total = res.paging.count;
        this.update();
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    getDicts(type) {
      getDictList(type).then(res => {
        if (type === 9) {
          this.sceneDict = res;
        } else if (type === 8) {
          this.applyDict = res;
        }
      });
    },
    search() {
      this.pageParams.offset = 0;
      this.getModelData();
    },
    clear() {
      this.params = {
        modelType: '',
        sceneDictIds: [],
        applyDictIds: [],
        keyword: ''
      };
      this.pageParams = {
        limit: 20,
        offset: 0
      };
      this.checkedList = [];
      this.$refs.table && this.$refs.table.clearSelection();
    },
    goDetails(data) {
      window.open(`/amapp/spsd/#/page_1064227888138539008?id=${data.id}`, '_blank');
    },
    filterData(data) {
      if (data && data.length) {
        return data.join(',');
      } else {
        return '-';
      }
    }
  }
};
</script>

<style lang="scss">
.yxtbiz-select-model__body {
  .gwnl-model-position_tree {
    .yxt-scrollbar__wrap {
      height: 340px !important;
    }
  }
}
</style>
