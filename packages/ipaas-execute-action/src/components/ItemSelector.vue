<template>
  <div class="item-selector">
    <!-- 事件/动作分组 Tab -->
    <yxt-tabs :value="activeTab" class="item-selector__tabs" @tab-click="handleTabClick">
      <yxt-tab-pane v-for="tab in tabs" :key="tab.key" :label="tab.label" :name="tab.key">
        <div class="item-selector__list-wrap" @scroll="handleScroll">
          <!-- 事件/动作列表 -->
          <div class="item-selector__list">
            <yxt-card v-for="item in currentItems" :key="item.id" class="item-selector__item"
              :class="{ 'is-selected': selectedItem && selectedItem.id === item.id }" shadow="hover"
              @click.native="selectItem(item)">
              <div class="item-selector__content">
                <div class="item-selector__info">
                  <div class="item-selector__name">{{ item.name }}</div>
                  <div class="item-selector__desc">{{ item.description }}</div>
                </div>
                <!-- 右上角选中角标 -->
                <yxt-svg v-if="selectedItem && selectedItem.id === item.id"
                  class="item-selector__selected-badge color-primary-6" width="20px" height="20px" icon-class="pick" />
              </div>
            </yxt-card>

            <!-- 加载中状态 -->
            <div v-if="pagination.loading" class="item-selector__loading">
              <yxt-icon name="loading" class="item-selector__loading-icon"></yxt-icon>
              <span>加载中...</span>
            </div>

            <!-- 没有更多数据提示 -->
            <div v-if="pagination.noMore" class="item-selector__no-more">
              没有更多数据了
            </div>
          </div>
        </div>
      </yxt-tab-pane>
    </yxt-tabs>
  </div>
</template>

<script>

export default {
  name: 'ItemSelector',

  props: {
    value: {
      type: Object,
      default: null
    },
    // 连接器ID
    connectorId: {
      type: String,
      default: ''
    },
    // 选择器类型：'action' 或 'event'
    type: {
      type: String,
      required: true,
      validator: value => ['action', 'event'].includes(value)
    },
    // 数据加载函数
    loadData: {
      type: Function,
      required: true
    },
    // 分组获取函数
    loadGroups: {
      type: Function,
      required: true
    }
  },

  data() {
    return {
      selectedItem: this.value,
      activeTab: 'all',
      tabs: [
        { key: 'all', label: '全部' }
      ],

      // 分页加载的数据
      itemsByType: {},

      // 分页相关
      pagination: {
        loading: false,
        noMore: false,
        currentPage: 1,
        pageSize: 8
      }
    };
  },

  computed: {
    currentItems() {
      return this.itemsByType[this.activeTab] || [];
    }
  },

  watch: {
    value(newVal) {
      this.selectedItem = newVal;
    },

    connectorId: {
      immediate: true,
      async handler(newConnectorId) {
        if (newConnectorId) {
          await this.generateTabs();
          this.resetPagination();
          this.loadMoreItems();
        }
      }
    }
  },

  methods: {
    // 选择事件/动作
    selectItem(item) {
      this.selectedItem = item;
      this.$emit('input', item);
      this.$emit('change', item);
    },

    // 处理滚动
    handleScroll(event) {
      const target = event.target;
      const scrollTop = target.scrollTop;
      const scrollHeight = target.scrollHeight;
      const clientHeight = target.clientHeight;

      // 滚动到底部时触发加载
      if (scrollTop + clientHeight >= scrollHeight - 10 && !this.pagination.loading && !this.pagination.noMore) {
        this.loadMoreItems(true);
      }
    },

    // 加载更多数据
    async loadMoreItems(append = false) {
      if (this.pagination.loading || this.pagination.noMore || !this.connectorId) return;

      this.pagination.loading = true;

      try {
        // 构建请求参数
        const requestData = {};

        // 如果不是"全部"tab，则按分组过滤
        if (this.activeTab !== 'all') {
          requestData.groupId = this.activeTab;
        }

        const params = {
          offset: (this.pagination.currentPage - 1) * this.pagination.pageSize,
          limit: this.pagination.pageSize,
          direction: 'asc'
        };

        // 调用传入的数据加载函数
        const response = await this.loadData(this.connectorId, requestData, params);

        // 检查响应数据
        if (response && response.datas && Array.isArray(response.datas)) {
          const dataList = response.datas;

          // 判断是否为最后一页
          const isLastPage = dataList.length < this.pagination.pageSize ||
            (response.paging && response.paging.offset + response.paging.limit >= response.paging.count);

          if (dataList.length > 0) {
            // 有数据时更新列表
            if (append) {
              this.appendItemData(dataList);
            } else {
              this.setItemData(dataList);
            }

            // 更新分页状态
            this.pagination.currentPage++;
          } else {
            // 第一页就没有数据的情况
            if (!append) {
              this.setItemData([]);
            }
          }

          this.pagination.noMore = isLastPage;
        } else {
          // API返回格式不正确或没有数据
          console.warn(`${this.type}API返回数据格式异常:`, response);

          if (!append) {
            this.setItemData([]);
          }
          this.pagination.noMore = true;
        }
      } catch (error) {
        console.error(`加载${this.type}失败:`, error);

        // 发生错误时也要清空数据（如果不是追加模式）
        if (!append) {
          this.setItemData([]);
        }
        this.pagination.noMore = true;

        // 显示错误提示
        this.$message && this.$message.error(error.message || `加载${this.type}数据失败，请稍后重试`);
      } finally {
        // 确保loading状态总是被重置
        this.pagination.loading = false;
      }
    },

    // 设置数据
    setItemData(list) {
      this.$set(this.itemsByType, this.activeTab, list);
    },

    // 追加数据
    appendItemData(list) {
      if (!this.itemsByType[this.activeTab]) {
        this.$set(this.itemsByType, this.activeTab, []);
      }
      this.itemsByType[this.activeTab].push(...list);
    },

    // 处理Tab点击
    handleTabClick(tab) {
      this.activeTab = tab.name;
      this.resetPagination();
      this.loadMoreItems();
    },

    // 根据连接器生成分组tabs
    async generateTabs() {
      if (!this.connectorId) {
        this.tabs = [{ key: 'all', label: '全部' }];
        return;
      }

      try {
        // 调用传入的分组获取函数
        const response = await this.loadGroups(this.connectorId);

        if (response && response.length > 0) {
          this.tabs = [
            { key: 'all', label: '全部' },
            ...response.map(group => ({
              key: group.id.toString(),
              label: group.name
            }))
          ];
        } else {
          this.tabs = [{ key: 'all', label: '全部' }];
        }
      } catch (error) {
        console.error('获取连接器分组失败:', error);
        this.tabs = [{ key: 'all', label: '全部' }];
      }

      this.activeTab = 'all';
    },

    // 重置分页状态
    resetPagination() {
      this.pagination.currentPage = 1;
      this.pagination.loading = false;
      this.pagination.noMore = false;
    }
  }
};
</script>