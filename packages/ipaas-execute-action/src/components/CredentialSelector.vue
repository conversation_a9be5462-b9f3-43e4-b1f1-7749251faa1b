<template>
  <div class="yxtbiz-ipaas-execute-action__credential-content">
    <!-- 凭证列表 -->
    <div class="yxtbiz-ipaas-execute-action__credential-list" v-loading="credentialLoading">
      <yxt-card v-for="credential in credentialList" :key="credential.id"
        class="yxtbiz-ipaas-execute-action__credential-item" :class="{
          'is-selected':
            selectedCredential && selectedCredential.id === credential.id
        }" shadow="hover" @click.native="selectCredential(credential)">
        <div class="yxtbiz-ipaas-execute-action__credential-card-content">
          <div class="yxtbiz-ipaas-execute-action__credential-info">
            <div class="yxtbiz-ipaas-execute-action__credential-name">
              {{ credential.name }}
            </div>
          </div>
          <!-- 选中角标 -->
          <yxt-svg v-if="selectedCredential && selectedCredential.id === credential.id"
            class="yxtbiz-ipaas-execute-action__selected-badge color-primary-6" width="20px" height="20px"
            icon-class="pick" />
        </div>
      </yxt-card>
    </div>
  </div>
</template>

<script>
import ipaasService from '../service';

export default {
  name: 'CredentialSelector',

  props: {
    value: {
      type: Object,
      default: null
    },
    connectorId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      credentialList: [],
      credentialLoading: false,
      selectedCredential: this.value
    };
  },

  watch: {
    value: {
      handler(newVal) {
        this.selectedCredential = newVal;
      },
      immediate: true
    },

    connectorId: {
      handler(newVal) {
        if (newVal) {
          this.loadCredentialList();
        }
      },
      immediate: true
    }
  },

  methods: {
    // 选择凭证
    selectCredential(credential) {
      this.selectedCredential = credential;
      this.$emit('input', credential);
      this.$emit('change', credential);
    },

    // 加载凭证列表
    async loadCredentialList() {
      if (!this.connectorId) return;

      this.credentialLoading = true;
      try {
        const response = await ipaasService.getConnectorCredentials(
          this.connectorId,
          'action'
        );

        if (response && Array.isArray(response)) {
          this.credentialList = response;
        } else {
          this.credentialList = [];
        }
      } catch (error) {
        console.error('加载凭证列表失败:', error);
        this.$message.error('加载凭证列表失败');
        this.credentialList = [];
      } finally {
        this.credentialLoading = false;
      }
    }
  }
};
</script>

<style scoped>
.yxtbiz-ipaas-execute-action__credential-content {
  padding: 20px;
}

.yxtbiz-ipaas-execute-action__credential-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.yxtbiz-ipaas-execute-action__credential-item {
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #e9e9e9;
}

.yxtbiz-ipaas-execute-action__credential-card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
}

.yxtbiz-ipaas-execute-action__credential-info {
  flex: 1;
}

.yxtbiz-ipaas-execute-action__credential-name {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.yxtbiz-ipaas-execute-action__selected-badge {
  flex-shrink: 0;
  margin-left: 12px;
}
</style>