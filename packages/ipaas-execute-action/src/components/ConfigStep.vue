<template>
  <div class="yxtbiz-ipaas-execute-action__config-content">
    <!-- 参数配置表格 -->
    <div
      v-loading="configLoading"
      class="yxtbiz-ipaas-execute-action__config-table"
    >
      <!-- 请求头参数配置 -->
      <div
        v-if="configParams.headerParams && configParams.headerParams.length > 0"
        class="yxtbiz-ipaas-execute-action__param-section"
      >
        <h4 class="yxtbiz-ipaas-execute-action__param-title">请求头参数</h4>
        <param-config-table
          v-model="configParams.headerParams"
          :ref-data="refData"
        />
      </div>

      <!-- URL查询参数配置 -->
      <div
        v-if="configParams.queryParams && configParams.queryParams.length > 0"
        class="yxtbiz-ipaas-execute-action__param-section"
      >
        <h4 class="yxtbiz-ipaas-execute-action__param-title">URL查询参数</h4>
        <param-config-table
          v-model="configParams.queryParams"
          :ref-data="refData"
        />
      </div>

      <!-- 路径参数配置 -->
      <div
        v-if="configParams.pathParams && configParams.pathParams.length > 0"
        class="yxtbiz-ipaas-execute-action__param-section"
      >
        <h4 class="yxtbiz-ipaas-execute-action__param-title">路径参数</h4>
        <param-config-table
          v-model="configParams.pathParams"
          :ref-data="refData"
        />
      </div>

      <!-- 请求体参数配置 -->
      <div
        v-if="configParams.bodyParams && configParams.bodyParams.length > 0"
        class="yxtbiz-ipaas-execute-action__param-section"
      >
        <h4 class="yxtbiz-ipaas-execute-action__param-title">请求体参数</h4>
        <param-config-table
          v-model="configParams.bodyParams"
          :ref-data="refData"
        />
      </div>

      <!-- 如果没有配置参数，显示提示 -->
      <div
        v-if="!hasAnyParams"
        class="yxtbiz-ipaas-execute-action__config-empty"
      >
        <yxt-icon
          name="info"
          class="yxtbiz-ipaas-execute-action__config-empty-icon"
        ></yxt-icon>
        <span>该动作无需额外配置参数</span>
      </div>
    </div>
  </div>
</template>

<script>
import ipaasService from '../service';
import ParamConfigTable from './ParamConfigTable.vue';

export default {
  name: 'ConfigStep',

  components: {
    ParamConfigTable
  },

  props: {
    value: {
      type: Object,
      default: () => ({
        headerParams: [],
        queryParams: [],
        pathParams: [],
        bodyParams: []
      })
    },
    refData: {
      type: Array,
      default: () => []
    },
    connectorId: {
      type: String,
      required: true
    },
    actionId: {
      type: String,
      required: true
    },
    credentialId: {
      type: String,
      default: ''
    },
    isBossDomain: {
      type: Boolean,
      default: false
    },
    taskData: {
      type: Object,
      default: null
    }
  },

  data() {
    return {
      configParams: { ...this.value },
      configLoading: false
    };
  },
  computed: {
    hasAnyParams() {
      return (
        this.configParams.headerParams.length > 0 ||
        this.configParams.queryParams.length > 0 ||
        this.configParams.pathParams.length > 0 ||
        this.configParams.bodyParams.length > 0
      );
    }
  },

  watch: {
    value: {
      handler(newVal) {
        if (JSON.stringify(newVal) === JSON.stringify(this.configParams)) {
          return;
        }

        this.configParams = { ...newVal };
      },
      deep: true,
      immediate: true
    },

    configParams: {
      handler(newVal) {
        this.$emit('input', newVal);
        this.$emit('change', newVal);
      },
      deep: true
    }
  },
  created() {
    this.loadConfigFields();
  },

  methods: {
    // 加载配置字段
    async loadConfigFields() {
      console.log(this.connectorId, this.actionId);
      if (!this.connectorId || !this.actionId) return;
      this.configLoading = true;
      try {
        // 调用执行动作详情接口
        const response = await ipaasService.getActionDetail(
          this.connectorId,
          this.actionId
        );

        if (response) {
          // 将接口返回的参数配置转换为表单字段格式
          this.configParams = {
            headerParams: this.setConfigsId(
              this.convertParamsToConfigFields(response.headerParams || []),
              this.configParams.headerParams || []
            ),
            queryParams: this.setConfigsId(
              this.convertParamsToConfigFields(response.queryParams || []),
              this.configParams.queryParams || []
            ),
            pathParams: this.setConfigsId(
              this.convertParamsToConfigFields(response.pathParams || []),
              this.configParams.pathParams || []
            ),
            bodyParams: this.setConfigsId(
              this.convertParamsToConfigFields(response.bodyParams || []),
              this.configParams.bodyParams || []
            ),
            responseBodyParams: this.setConfigsId(
              response.responseBodyParams || [],
              (this.taskData &&
                this.taskData.payload &&
                this.taskData.payload.response) ||
                []
            )
          };
        } else {
          this.configParams = {
            headerParams: [],
            queryParams: [],
            pathParams: [],
            bodyParams: []
          };
        }
      } catch (error) {
        console.error('加载配置字段失败:', error);
        this.$message.error('加载配置字段失败');
        this.configParams = {
          headerParams: [],
          queryParams: [],
          pathParams: [],
          bodyParams: []
        };
      } finally {
        this.configLoading = false;
      }
    },

    // 设置id
    setConfigsId(list, dataList) {
      list.forEach(item => {
        const target = dataList.find(
          d => (d.field === item.paramKey || d.paramKey === item.paramKey) && (d.fieldType === this.getFieldTypeByParamType(item.paramType) || d.paramType === item.paramType)
        );
        if (target) {
          item.id = target.id;
          item._type = target._type;
          item.mappingValue = target.mappingValue;
          item.constantValue = target.constantValue;
          if (item.properties && item.properties.length) {
            this.setConfigsId(item.properties, target.properties || []);
          }
        }
      });
      return list;
    },

    // 将API参数配置转换为配置字段格式
    convertParamsToConfigFields(params) {
      if (!Array.isArray(params)) return [];

      let keyCounter = 1;

      const processParams = (paramList, parentType = '', level = 0) => {
        const indent = '  '.repeat(level);
        console.log(
          `${indent}处理参数列表 (${paramList.length}项):`,
          paramList
        );

        return paramList.map(param => {
          console.log(`${indent}处理参数:`, param.paramName, param);

          const configField = {
            paramKey: param.paramKey,
            paramName: param.paramName || param.paramKey,
            paramType: param.paramType,
            required: param.required || false,
            description: param.description,
            constantValue: '', // 常量值
            mappingValue: null, // 字段映射值
            valueType: 2, // 默认使用常量，1-字段映射，2-常量
            tableKey: `field_${keyCounter++}`,
            fieldType: this.getFieldTypeByParamType(param.paramType),
            properties: []
          };

          // 尝试多种可能的子属性字段名
          const possibleChildKeys = [
            'properties',
            'children',
            'subParams',
            'fields',
            'parameters',
            'items',
            'schema',
            'subFields'
          ];

          let childProperties = [];
          let foundChildKey = null;

          for (const key of possibleChildKeys) {
            if (
              param[key] &&
              Array.isArray(param[key]) &&
              param[key].length > 0
            ) {
              childProperties = param[key];
              foundChildKey = key;
              break;
            }
          }

          console.log(`${indent}  -> 子属性检查:`, {
            paramName: param.paramName,
            paramType: param.paramType,
            foundChildKey,
            childCount: childProperties.length,
            childProperties: childProperties
          });

          if (childProperties.length > 0) {
            configField.properties = processParams(
              childProperties,
              param.paramType,
              level + 1
            );
            console.log(
              `${indent}  -> ✅ 字段 ${param.paramName} 有 ${childProperties.length} 个子属性，来源: ${foundChildKey}`
            );
          } else if (
            param.paramType === 'object' ||
            param.paramType === 'array_object'
          ) {
            console.log(
              `${indent}  -> ⚠️ 对象类型字段 ${param.paramName} 但无子属性数据`
            );
            // 检查是否有嵌套的对象结构
            console.log(
              `${indent}  -> 完整参数对象:`,
              JSON.stringify(param, null, 2)
            );
          }

          return configField;
        });
      };

      const result = processParams(params);
      console.log('🔄 最终转换结果:', result);
      return result;
    },

    // 根据参数类型获取字段类型
    getFieldTypeByParamType(paramType) {
      const typeMap = {
        string: 1,
        number: 2,
        boolean: 4,
        object: 5,
        array_string: 6,
        array_number: 7,
        array_boolean: 7,
        array_object: 9
      };
      return typeMap[paramType] || 1;
    }
  }
};
</script>

<style scoped>
.yxtbiz-ipaas-execute-action__config-content {
  padding: 20px;
}

.yxtbiz-ipaas-execute-action__config-table {
  min-height: 200px;
}

.yxtbiz-ipaas-execute-action__param-section {
  margin-bottom: 32px;
}

.yxtbiz-ipaas-execute-action__param-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.yxtbiz-ipaas-execute-action__config-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #8c8c8c;
  font-size: 14px;
}

.yxtbiz-ipaas-execute-action__config-empty-icon {
  margin-right: 8px;
  font-size: 16px;
}
</style>
