<template>
  <div class="yxtbiz-param-config-table">
    <yxt-table 
      :data="tableData" 
      class="yxtbiz-param-config-table__table"
      row-key="tableKey" 
      default-expand-all 
      :indent="16"
      :tree-props="{ children: 'properties', hasChildren: 'hasChildren' }"
      :expand-row-keys="expandedKeys"
    >
      <yxt-table-column prop="paramName" label="字段名称" min-width="150"></yxt-table-column>
      <yxt-table-column prop="paramKey" label="字段编码" min-width="150"></yxt-table-column>
      <yxt-table-column prop="paramType" label="字段类型" min-width="120"></yxt-table-column>
      <yxt-table-column label="是否必填" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.required ? '是' : '否' }}</span>
        </template>
      </yxt-table-column>
      <yxt-table-column label="值" min-width="300">
        <template slot-scope="scope">
          <!-- 当父节点需要禁用子节点时显示 '-' -->
          <span v-if="shouldDisableChildValue(scope.row)" style="color: #909399;">-</span>
          <!-- 其他类型显示配置选项 -->
          <div v-else class="yxtbiz-param-config-table__value-wrapper">
            <!-- 值类型选择 -->
            <yxt-select 
              :value="scope.row._type || 1" 
              @input="handleValueTypeChange(scope.row, $event)"
              size="small" 
              style="width: 104px; margin-right: 8px;"
            >
              <yxt-option label="字段映射" :value="1"></yxt-option>
              <yxt-option label="常量" :value="2"></yxt-option>
            </yxt-select>
            
            <!-- 根据值类型显示不同的输入控件 -->
            <!-- 字段映射选择 -->
            <yxtbiz-field-mapping
              v-if="(scope.row._type || 1) === 1"
              :value="scope.row.mappingValue"
              @input="handleMappingValueChange(scope.row, $event)"
              :ref-data="refData"
              :field-type="getFieldTypeByParamType(scope.row.paramType)"
              style="width: 200px;"
            />
            
            <!-- 常量值输入 -->
            <yxt-input 
              v-if="(scope.row._type || 1) === 2" 
              :value="scope.row.constantValue || ''"
              @input="handleConstantValueChange(scope.row, $event)"
              size="small"
              style="width: 200px;" 
              :placeholder="`请输入${scope.row.paramName}`"
            />
          </div>
        </template>
      </yxt-table-column>
    </yxt-table>
  </div>
</template>

<script>
import FieldMapping from './FieldMapping.vue';

export default {
  name: 'YxtbizParamConfigTable',
  components: {
    'yxtbiz-field-mapping': FieldMapping
  },

  props: {
    // 表格数据源，支持双向绑定
    value: {
      type: Array,
      default: () => []
    },
    // 引用数据源
    refData: {
      type: Array,
      default: () => []
    }
  },

  computed: {
    // 内部表格数据
    tableData: {
      get() {
        return this.value || [];
      },
      set(newValue) {
        this.$emit('input', newValue);
      }
    },

    // 展开的键
    expandedKeys() {
      const keys = [];
      const collectExpandKeys = (params) => {
        params.forEach(param => {
          if (param.properties && param.properties.length > 0) {
            keys.push(param.tableKey);
            collectExpandKeys(param.properties);
          }
        });
      };
      collectExpandKeys(this.tableData);
      return keys;
    }
  },

  methods: {
    // 处理值类型变化
    handleValueTypeChange(row, newType) {
      this.$set(row, '_type', newType);
      // 清空其他类型的值
      if (newType === 1) {
        // 字段映射
        this.$set(row, 'constantValue', '');
      } else if (newType === 2) {
        // 常量
        this.$set(row, 'mappingValue', null);
      }
      this.emitChange();
    },

    // 处理常量值变化
    handleConstantValueChange(row, value) {
      this.$set(row, 'constantValue', value);
      this.emitChange();
    },

    // 处理字段映射值变化
    handleMappingValueChange(row, value) {
      this.$set(row, 'mappingValue', value);
      this.emitChange();
    },

    // 触发数据变化事件
    emitChange() {
      this.$emit('input', this.tableData);
      this.$emit('change', this.tableData);
    },

    // 根据参数类型获取字段类型
    getFieldTypeByParamType(paramType) {
      const typeMap = {
        'string': 1,
        'number': 2,
        'boolean': 4,
        'object': 5,
        'array_string': 6,
        'array_number': 7,
        'array_boolean': 7,
        'array_object': 9
      };
      return typeMap[paramType] || 1;
    },

    // 判断是否应该禁用子节点的值设置
    shouldDisableChildValue(row) {
      // 情况1: 如果paramType是array_object，子节点直接显示"-"
      const parent = this.findParent(row);
      if (parent && parent.paramType === 'array_object') {
        return true;
      }
      // 情况2: 如果父节点paramType是object且设置了值，子节点显示"-"
      if (parent && parent.paramType === 'object') {
        const hasValue = this.parentHasValue(parent);
        return hasValue;
      }
      return false;
    },

    // 判断父节点是否设置了值
    parentHasValue(parentRow) {
      const type = parentRow._type || 1;
      if (type === 1) {
        // 字段映射类型，检查mappingValue
        return parentRow.mappingValue && (
          (typeof parentRow.mappingValue === 'object' && parentRow.mappingValue.field) ||
          (typeof parentRow.mappingValue === 'string' && parentRow.mappingValue.trim() !== '')
        );
      } else if (type === 2) {
        // 常量类型，检查constantValue
        return parentRow.constantValue && parentRow.constantValue.trim() !== '';
      }
      return false;
    },

    // 找到父节点
    findParent(row) {
      let parent = null;
      const find = (params) => {
        params.forEach(param => {
          if (param.properties && param.properties.length > 0) {
            if (param.properties.some(p => p.tableKey === row.tableKey)) {
              parent = param;
            } else {
              find(param.properties);
            }
          }
        });
      };
      find(this.tableData);
      return parent;
    }
  }
};
</script> 