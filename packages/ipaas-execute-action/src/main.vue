<template>
  <div class="yxtbiz-ipaas-execute-action">
    <!-- 步骤条 -->
    <yxt-steps :active="currentStep - 1" operate alloperate type="navigation" finish-status="success"
      class="yxtbiz-ipaas-execute-action__steps" :beforeClickStep="beforeActiveChange">
      <yxt-step title="选择连接器"></yxt-step>
      <yxt-step title="选择执行动作"></yxt-step>
      <yxt-step v-if="!isBossDomain()" title="选择凭证"></yxt-step>
      <yxt-step title="配置"></yxt-step>
    </yxt-steps>

    <!-- 内容区域 -->
    <div class="yxtbiz-ipaas-execute-action__content">
      <!-- 第一步：选择连接器 -->
      <div v-if="currentStep === 1" class="yxtbiz-ipaas-execute-action__step-content">
        <connector-selector v-model="selectedConnector" :activeTab.sync="activeConnectorTab"
          @change="handleConnectorChange" class="yxtbiz-ipaas-execute-action__tabs" />

        <!-- 底部按钮 -->
        <!-- <div class="yxtbiz-ipaas-execute-action__footer">
          <yxt-button type="primary" :disabled="!selectedConnector" @click="nextStep">
            下一步
          </yxt-button>
        </div> -->
      </div>
      <!-- 第二步：选择执行动作 -->
      <div v-if="currentStep === 2" class="yxtbiz-ipaas-execute-action__step-content">
        <item-selector v-model="selectedAction" :connector-id="selectedConnector ? selectedConnector.id : ''"
          type="action" :load-data="loadActionData" :load-groups="loadConnectorGroups" @change="handleActionChange"
          class="yxtbiz-ipaas-execute-action__tabs" />

        <!-- 底部按钮 -->
        <!-- <div class="yxtbiz-ipaas-execute-action__footer">
          <yxt-button @click="prevStep">上一步</yxt-button>
          <yxt-button type="primary" :disabled="!selectedAction" @click="nextStep">
            下一步
          </yxt-button>
        </div> -->
      </div>
      <!-- 第三步：选择凭证 -->
      <div v-if="currentStep === 3 && !isBossDomain()" class="yxtbiz-ipaas-execute-action__step-content">
        <credential-selector v-model="selectedCredential" :connector-id="selectedConnector ? selectedConnector.id : ''"
          @change="handleCredentialChange" />
      </div>
      <!-- 第四步：配置 -->
      <div v-if="(currentStep === 3 && isBossDomain()) || currentStep === 4"
        class="yxtbiz-ipaas-execute-action__step-content">
        <config-step v-model="configParams" :ref-data="refData"
          :connector-id="selectedConnector ? selectedConnector.id : ''"
          :action-id="selectedAction ? selectedAction.id : ''"
          :credential-id="selectedCredential ? selectedCredential.id : ''" :is-boss-domain="isBossDomain()"
          :task-data="taskData" @change="handleConfigChange" />
      </div>
    </div>
  </div>
</template>

<script>
import ipaasService from './service';
import ConnectorSelector from './components/ConnectorSelector.vue';
import ItemSelector from './components/ItemSelector.vue';
import ParamConfigTable from './components/ParamConfigTable.vue';
import CredentialSelector from './components/CredentialSelector.vue';
import ConfigStep from './components/ConfigStep.vue';

// 判断当前机构是否为 boss 机构
export const isBossDomain = () => {
  const isBoss = localStorage.getItem('_am_isBoss');
  if (isBoss === null) {
    return true;
  }

  try {
    return Boolean(JSON.parse(isBoss));
  } catch (error) {
    console.error('Error parsing _am_isBoss:', error);
    return true;
  }
};
export default {
  name: 'YxtbizIpaasExecuteAction',

  components: {
    ConnectorSelector,
    ItemSelector,
    ParamConfigTable,
    CredentialSelector,
    ConfigStep
  },

  data() {
    return {
      refData: [],
      // refData: [
      //   {
      //     "id": "0bbf7b35-192f-4cc1-af15-ded9398d7e8f",
      //     "nodeType": 1,
      //     "nodeName": "钉钉创建部门通知",
      //     "inputs": [
      //       {
      //         "id": "1932268970068236290",
      //         "field": "EventType",
      //         "fieldType": 1,
      //         "desc": "EventType",
      //         "value": null,
      //         "defaultValue": null,
      //         "required": true,
      //         "properties": []
      //       },
      //       {
      //         "id": "1932268970068236291",
      //         "field": "EventTime",
      //         "fieldType": 2,
      //         "desc": "EventTime",
      //         "value": null,
      //         "defaultValue": null,
      //         "required": true,
      //         "properties": []
      //       },
      //       {
      //         "id": "1932268970068236292",
      //         "field": "CorpId",
      //         "fieldType": 1,
      //         "desc": "CorpId",
      //         "value": null,
      //         "defaultValue": null,
      //         "required": true,
      //         "properties": []
      //       },
      //       {
      //         "id": "1932268970068236293",
      //         "field": "BizId",
      //         "fieldType": 1,
      //         "desc": "BizId",
      //         "value": null,
      //         "defaultValue": null,
      //         "required": true,
      //         "properties": []
      //       },
      //       {
      //         "id": "1932268970068236294",
      //         "field": "eventId",
      //         "fieldType": 1,
      //         "desc": "eventId",
      //         "value": null,
      //         "defaultValue": null,
      //         "required": true,
      //         "properties": []
      //       },
      //       {
      //         "id": "1932268970068236295",
      //         "field": "deptId",
      //         "fieldType": 7,
      //         "desc": "deptId",
      //         "value": null,
      //         "defaultValue": null,
      //         "required": true,
      //         "properties": []
      //       }
      //     ],
      //     "outputs": null
      //   }
      // ],
      // 当前步骤
      currentStep: 1,

      // 选中的连接器、动作、凭证
      selectedConnector: null,
      activeConnectorTab: 'official',
      selectedAction: null,
      selectedCredential: null,

      // 第四步：配置相关
      configParams: {
        headerParams: [],
        queryParams: [],
        pathParams: [],
        bodyParams: []
      },

      // 流程引擎规范 - 节点相关数据
      nodeId: '',
      nodeName: '',
      taskData: null,
      subNodeReqList: []
    };
  },

  computed: {
    // 所有连接器列表（用于恢复选中状态）
    allConnectors() {
      // 这里可以从子组件获取或缓存连接器数据
      return [];
    }
  },

  methods: {
    isBossDomain,
    // 流程引擎规范 - 初始化节点数据
    setData(data) {
      if (data) {
        this.nodeId = data.id || '';
        this.nodeName = data.nodeName || '';
        this.subNodeReqList = data.subNodeReqList || [];
        this.taskData = data.taskData || {};
        if (data.refData.length > 0) {
          this.refData = data.refData || [];
        }
        // 解析任务数据，恢复选中状态
        if (data.taskData && data.taskData.extension) {
          this.taskData = data.taskData;
          const extension = data.taskData.extension;

          // 恢复连接器选择
          if (extension.connectorId && extension.connectorName) {
            this.selectedConnector = {
              id: extension.connectorId,
              name: extension.connectorName
            };
            this.activeConnectorTab =
              extension.activeConnectorTab || 'official';
          }

          // 恢复动作选择
          if (
            extension.actionId &&
            extension.actionName &&
            this.selectedConnector
          ) {
            this.selectedAction = {
              id: extension.actionId,
              name: extension.actionName,
              group: extension.actionGroup
            };
          }

          // 恢复凭证选择
          if (
            extension.credentialId &&
            extension.credentialName &&
            this.selectedAction
          ) {
            this.selectedCredential = {
              id: extension.credentialId,
              name: extension.credentialName,
              type: extension.credentialType
            };
          }

          // 恢复配置数据
          if (extension.config) {
            this.configParams = { ...extension.config };
            if (this.selectedCredential) {
              this.currentStep = 4;
            } else {
              this.currentStep = 3;
            }
          } else if (this.selectedCredential) {
            this.currentStep = 3;
          } else if (this.selectedAction) {
            this.currentStep = 2;
          }
        }
      }
    },

    // 流程引擎规范 - 获取节点任务内容
    getData() {
      const nodeLabel = [];
      let taskData = {
        extension: {},
        payload: {}
      };
      if (this.configParams) {
        taskData.payload.url = `/intghub-record/api/v1/connectors/${this.selectedConnector.id}/actions/${this.selectedAction.id}/execute`;
        taskData.payload.method = 'POST';
        // taskData.payload.header = this.getHeaderAndRequest(this.configParams.headerParams);
        taskData.payload.body = { content: JSON.stringify(this.getBody()) };
        // taskData.payload.request = this.getHeaderAndRequest([...this.configParams.pathParams, ...this.configParams.queryParams]);
        taskData.payload.response = this.convertResponse(
          this.configParams.responseBodyParams
        );
      }

      if (this.selectedConnector) {
        taskData.extension.connectorId = this.selectedConnector.id;
        taskData.extension.connectorName = this.selectedConnector.name;
        taskData.extension.activeConnectorTab = this.activeConnectorTab;
        nodeLabel.push({
          label: '连接器',
          value: this.selectedConnector.name
        });
      }

      if (this.selectedAction) {
        taskData.extension.actionId = this.selectedAction.id;
        taskData.extension.actionName = this.selectedAction.name;
        taskData.extension.actionGroup = this.selectedAction.group;
        nodeLabel.push({
          label: '执行动作',
          value: this.selectedAction.name
        });
      }

      if (this.selectedCredential) {
        taskData.extension.credentialId = this.selectedCredential.id;
        taskData.extension.credentialName = this.selectedCredential.name;
        taskData.extension.credentialType = this.selectedCredential.type;
      }

      if (this.configParams && Object.keys(this.configParams).length > 0) {
        taskData.extension.config = { ...this.configParams };
      }

      // 设置节点标签，用于画布显示
      taskData.extension.nodeLabel = nodeLabel;

      return {
        taskData
      };
    },
    beforeActiveChange(cb, index) {
      // 选择器1、执行动作2、凭证3、配置3/4
      const step = index + 1;
      if (step === 1) {
        this.currentStep = step;
        cb();
      } else if (step === 2 && this.selectedConnector) {
        this.currentStep = step;
        cb();
      } else if (step === 3 && this.selectedConnector && this.selectedAction) {
        this.currentStep = step;
        cb();
      } else if (
        step === 4 &&
        this.selectedConnector &&
        this.selectedAction &&
        this.selectedCredential
      ) {
        this.currentStep = step;
        cb();
      }
    },

    // 流程引擎规范 - 获取校验结果
    validate() {
      // 必须完成所有四个步骤
      if (!this.selectedConnector) {
        this.$message.warning('请选择连接器');
        return false;
      }
      if (!this.selectedAction) {
        this.$message.warning('请选择执行动作');
        return false;
      }
      if (!this.isBossDomain() && !this.selectedCredential) {
        this.$message.warning('请选择凭证');
        return false;
      }
      return true;
    },

    convertResponse(data) {
      const setProp = items => {
        return items.map(item => {
          const hasProp = item.properties && item.properties.length > 0;
          return {
            id: item.id,
            field: item.paramKey,
            fieldType: this.getFieldTypeByParamType(item.paramType),
            type: hasProp ? 3 : 1,
            value: hasProp
              ? {
                field: item.paramKey
              }
              : {
                field: item.paramKey,
                fieldType: this.getFieldTypeByParamType(item.paramType)
              },
            properties: hasProp ? setProp(item.properties) : []
          };
        });
      };
      return data.map(item => {
        const hasProp = item.properties && item.properties.length > 0;
        return {
          id: item.id,
          field: item.paramKey,
          fieldType: this.getFieldTypeByParamType(item.paramType),
          type: hasProp ? 3 : 1,
          value: hasProp
            ? {
              field: item.paramKey
            }
            : {
              field: item.paramKey,
              fieldType: this.getFieldTypeByParamType(item.paramType)
            },
          properties: hasProp ? setProp(item.properties) : []
        };
      });
    },

    getHeaderAndRequest(data) {
      const getValue = item => {
        if (item._type === 2) {
          return {
            type: 2,
            value: item.constantValue
          };
        } else if (item._type === 1) {
          return {
            field:
              item.mappingValue &&
              item.mappingValue.path &&
              item.mappingValue.path.join('.'),
            type: 1,
            fieldType: this.getFieldTypeByParamType(item.paramType)
          };
        } else {
          return {
            field: item.referenceValue && item.referenceValue.field,
            type: 1,
            fieldType: this.getFieldTypeByParamType(item.paramType)
          };
        }
      };
      return data.map(item => {
        return {
          key: item.paramKey,
          type: item._type === 2 ? 2 : 1,
          fieldType: this.getFieldTypeByParamType(item.paramType),
          value: getValue(item)
        };
      });
    },
    // 根据参数类型获取字段类型
    getFieldTypeByParamType(paramType) {
      const typeMap = {
        string: 1,
        number: 2,
        boolean: 4,
        object: 5,
        array_string: 6,
        array_number: 7,
        array_boolean: 7,
        array_object: 9
      };
      return typeMap[paramType] || 1;
    },
    getBody() {
      return {
        headerParams: this.getOtherParams(this.configParams.headerParams),
        queryParams: this.getOtherParams(this.configParams.queryParams),
        pathParams: this.getOtherParams(this.configParams.pathParams),
        bodyParams: this.getBodyParams(this.configParams.bodyParams),
        credentialPid: this.selectedCredential && this.selectedCredential.id
      };
    },
    getOtherParams(data) {
      const getValue = item => {
        if (item._type === 2) {
          return item.constantValue;
        } else {
          if (item.mappingValue) {
            const nodeId =
              item.mappingValue.originalData &&
              item.mappingValue.originalData.nodeId;
            const path = JSON.parse(JSON.stringify(item.mappingValue.path));
            const index = path.indexOf(nodeId);
            if (index > -1) {
              path.splice(index, 1);
            }
            return `$var=${path.join('.')}$`;
          }
          return '';
        }
      };
      return data.map(item => {
        return {
          key: item.paramKey,
          value: getValue(item)
        };
      });
    },
    getBodyParams(data) {
      if (!data || data.length === 0) return {};
      const set = item => {
        let content = '';
        if (item._type === 2) {
          content = item.constantValue;
        } else {
          if (item.mappingValue) {
            const nodeId =
              item.mappingValue.originalData &&
              item.mappingValue.originalData.nodeId;
            const path = JSON.parse(JSON.stringify(item.mappingValue.path));
            const index = path.indexOf(nodeId);
            if (index > -1) {
              path.splice(index, 1);
            }
            content = `$var=${path.join('.')}$`;
          } else {
            if (item.paramKey === 'object') {
              content = this.getBodyParams(item.properties);
            }
          }
        }
        return content;
      };
      const content = {};
      data.forEach(item => {
        content[item.paramKey] = set(item);
      });
      return content;
    },

    // 连接器变化处理
    handleConnectorChange(connector, changed) {
      if (changed) {
        // 清空之后的选择
        this.selectedAction = null;
        this.selectedCredential = null;
        this.configParams = {
          headerParams: [],
          queryParams: [],
          pathParams: [],
          bodyParams: []
        };
      }
      this.selectedConnector = connector;
      this.nextStep();
    },

    // 动作变化处理
    handleActionChange(action) {
      if (this.selectedAction && this.selectedAction.id !== action.id) {
        // 清空之后的选择
        // this.selectedCredential = null;
        this.configParams = {
          headerParams: [],
          queryParams: [],
          pathParams: [],
          bodyParams: []
        };
      }
      this.selectedAction = action;
      this.nextStep();
    },

    // 凭证变化处理
    handleCredentialChange(credential) {
      this.selectedCredential = credential;
      this.nextStep();
    },

    // 配置变化处理
    handleConfigChange(config) {
      this.configParams = config;
    },

    // 下一步逻辑
    async nextStep() {
      if (this.currentStep === 1 && this.selectedConnector) {
        this.currentStep = 2;
      } else if (this.currentStep === 2 && this.selectedAction) {
        this.currentStep = 3;
      } else if (this.currentStep === 3 && this.selectedCredential) {
        this.currentStep = 4;
      }
    },

    // 上一步逻辑
    prevStep() {
      if (this.currentStep > 1) {
        if (this.currentStep === 4 && this.isBossDomain()) {
          this.currentStep = 2;
        } else {
          this.currentStep--;
        }
      }
    },

    // 确认选择（最终提交）
    async confirmSelection() {
      // 验证配置参数
      // if (this.hasAnyParams) {
      //   try {
      //     this.validateConfigParams();
      //   } catch (error) {
      //     this.$message.error('请完善配置信息');
      //     return;
      //   }
      // }

      if (this.selectedConnector && this.selectedAction) {
        const result = {
          connector: this.selectedConnector,
          action: this.selectedAction,
          credential: this.selectedCredential,
          config: this.configParams
        };
        console.log('this.getData()', this.getData());
        this.$emit('confirm', result);
      }
    },

    // 验证配置参数
    validateConfigParams() {
      const allParams = [
        ...this.configParams.headerParams,
        ...this.configParams.queryParams,
        ...this.configParams.pathParams,
        ...this.configParams.bodyParams
      ];

      // 递归验证参数
      const validateParamList = paramList => {
        for (const param of paramList) {
          // 跳过对象和数组对象类型的验证，因为它们显示 '-' 不需要配置值
          if (param.paramType === 'object' && param._type === 1) {
            // 仅递归验证子属性
            if (param.properties && param.properties.length > 0) {
              validateParamList(param.properties);
            }
            continue;
          }

          // 验证其他类型的必填字段
          if (param.required) {
            const valueType = param._type || 3; // 默认为引用类型

            if (valueType === 1) {
              // 字段映射模式
              if (!param.mappingValue) {
                throw new Error(`请为必填字段"${param.paramName}"选择字段映射`);
              }
            } else if (valueType === 2) {
              // 常量模式
              if (!param.constantValue || param.constantValue.trim() === '') {
                throw new Error(`请填写必填字段"${param.paramName}"`);
              }
            } else if (valueType === 3) {
              // 引用模式
              if (!param.referenceValue) {
                throw new Error(`请为必填字段"${param.paramName}"选择引用`);
              }
            }
          }

          // 递归验证子属性
          if (param.properties && param.properties.length > 0) {
            validateParamList(param.properties);
          }
        }
      };

      validateParamList(allParams);
    },

    // 加载执行动作数据的函数
    async loadActionData(connectorId, requestData, params) {
      return await ipaasService.getActionList(connectorId, requestData, params);
    },

    // 加载连接器分组的函数
    async loadConnectorGroups(connectorId) {
      return await ipaasService.getConnectorGroups(connectorId);
    },

    // 获取展开的键
    getExpandedKeys(params) {
      const keys = [];
      const expand = param => {
        if (param.properties.length) {
          keys.push(param.tableKey);
          if (param.properties && Array.isArray(param.properties)) {
            param.properties.forEach(child => expand(child));
          }
        }
      };
      params.forEach(param => expand(param));
      return keys;
    }
  }
};
</script>
