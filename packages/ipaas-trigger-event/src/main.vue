<template>
  <div class="yxtbiz-ipaas-trigger-event">
    <!-- 步骤条 -->
    <yxt-steps :active="currentStep - 1" operate alloperate type="navigation" finish-status="success"
      class="yxtbiz-ipaas-trigger-event__steps" :beforeClickStep="beforeActiveChange">
      <yxt-step title="选择连接器"></yxt-step>
      <yxt-step title="选择触发事件"></yxt-step>
      <yxt-step v-if="showCredential" title="凭证配置"></yxt-step>
    </yxt-steps>

    <!-- 内容区域 -->
    <div class="yxtbiz-ipaas-trigger-event__content">
      <!-- 第一步：选择连接器 -->
      <div v-if="currentStep === 1" class="yxtbiz-ipaas-trigger-event__step-content">
        <connector-selector v-model="selectedConnector" :activeTab.sync="activeConnectorTab" showTool
          @change="selectConnector" class="yxtbiz-ipaas-trigger-event__tabs" />

        <!-- 底部按钮 -->
        <!-- <div class="yxtbiz-ipaas-trigger-event__footer">
          <yxt-button type="primary" :disabled="!selectedConnector" @click="nextStep">
            下一步
          </yxt-button>
        </div> -->
      </div>

      <!-- 第二步：选择触发事件 -->
      <div v-if="currentStep === 2" class="yxtbiz-ipaas-trigger-event__step-content">
        <!-- 定时设置 -->
        <template v-if="selectedConnector.id === '-1'">
          <yxt-form label-position="top">
            <yxt-form-item label="cron表达式">
              <yxt-input v-model="scheduleCron" placeholder="请输入cron表达式" />
            </yxt-form-item>
          </yxt-form>
        </template>
        <template v-else>
          <!-- 触发事件分组 Tab -->
          <yxt-tabs :value="activeEventTab" class="yxtbiz-ipaas-trigger-event__tabs" @tab-click="handleEventTabClick">
            <yxt-tab-pane v-for="tab in eventTabs" :key="tab.key" :label="tab.label" :name="tab.key">
              <div class="yxtbiz-ipaas-trigger-event__event-list-wrap" @scroll="handleEventScroll">
                <!-- 触发事件列表 -->
                <div class="yxtbiz-ipaas-trigger-event__event-list">
                  <yxt-card v-for="event in currentEvents" :key="event.id"
                    class="yxtbiz-ipaas-trigger-event__event-item" :class="{
                      'is-selected':
                        selectedEvent && selectedEvent.id === event.id
                    }" shadow="hover" @click.native="selectEvent(event)">
                    <div class="yxtbiz-ipaas-trigger-event__event-content">
                      <div class="yxtbiz-ipaas-trigger-event__event-info">
                        <div class="yxtbiz-ipaas-trigger-event__event-name">
                          {{ event.name }}
                        </div>
                        <div class="yxtbiz-ipaas-trigger-event__event-desc">
                          {{ event.description }}
                        </div>
                      </div>
                      <!-- 右上角选中角标或加载状态 -->
                      <yxt-icon v-if="
                        selectedEvent &&
                        selectedEvent.id === event.id &&
                        eventDetailLoading
                      " name="loading" class="yxtbiz-ipaas-trigger-event__loading-badge"></yxt-icon>
                      <yxt-svg v-else-if="
                        selectedEvent && selectedEvent.id === event.id
                      " class="yxtbiz-ipaas-trigger-event__selected-badge color-primary-6" width="20px" height="20px"
                        icon-class="pick" />
                    </div>
                  </yxt-card>

                  <!-- 加载中状态 -->
                  <div v-if="eventPagination.loading" class="yxtbiz-ipaas-trigger-event__loading">
                    <yxt-icon name="loading" class="yxtbiz-ipaas-trigger-event__loading-icon"></yxt-icon>
                    <span>加载中...</span>
                  </div>

                  <!-- 没有更多数据提示 -->
                  <div v-if="eventPagination.noMore" class="yxtbiz-ipaas-trigger-event__no-more">
                    没有更多数据了
                  </div>
                </div>
              </div>
            </yxt-tab-pane>
          </yxt-tabs>
        </template>

        <!-- 底部按钮 -->
        <!-- <div class="yxtbiz-ipaas-trigger-event__footer"> -->
        <!-- <yxt-button @click="prevStep">上一步</yxt-button> -->
        <!-- <yxt-button type="primary" :disabled="!selectedEvent" @click="confirmSelection">
            确认选择
          </yxt-button> -->
        <!-- </div> -->
      </div>

      <!-- 第三步：选择凭证 -->
      <div v-if="currentStep === 3" class="yxtbiz-ipaas-trigger-event__step-content">
        <div class="yxtbiz-ipaas-trigger-event__credential-content">
          <!-- 凭证列表 -->
          <div class="yxtbiz-ipaas-trigger-event__credential-list" v-loading="credentialLoading">
            <yxt-card v-for="credential in credentialList" :key="credential.id"
              class="yxtbiz-ipaas-trigger-event__credential-item" :class="{
                'is-selected':
                  selectedCredential && selectedCredential.id === credential.id
              }" shadow="hover" @click.native="selectCredential(credential)">
              <div class="yxtbiz-ipaas-trigger-event__credential-card-content">
                <div class="yxtbiz-ipaas-trigger-event__credential-info">
                  <div class="yxtbiz-ipaas-trigger-event__credential-name">
                    {{ credential.name }}
                  </div>
                </div>
                <!-- 选中角标 -->
                <yxt-svg v-if="
                  selectedCredential &&
                  selectedCredential.id === credential.id
                " class="yxtbiz-ipaas-trigger-event__selected-badge color-primary-6" width="20px" height="20px"
                  icon-class="pick" />
              </div>
            </yxt-card>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 判断当前机构是否为 boss 机构
export const isBossDomain = () => {
  const isBoss = localStorage.getItem('_am_isBoss');
  if (isBoss === null) {
    return true;
  }

  try {
    return Boolean(JSON.parse(isBoss));
  } catch (error) {
    console.error('Error parsing _am_isBoss:', error);
    return true;
  }
};

import ipaasService from './service';

import ConnectorSelector from '../../ipaas-execute-action/src/components/ConnectorSelector.vue';

export default {
  name: 'YxtbizIpaasTriggerEvent',

  components: {
    ConnectorSelector
  },

  data() {
    return {
      // 当前步骤
      currentStep: 1,

      // 连接器相关
      selectedConnector: null,
      activeConnectorTab: 'official',

      // 触发事件相关
      activeEventTab: 'all',
      selectedEvent: null,
      scheduleCron: '',
      eventTabs: [{ key: 'all', label: '全部' }],

      // 流程引擎规范 - 节点相关数据
      nodeId: '',
      nodeName: '',
      taskData: null,
      subNodeReqList: [],
      refData: [],

      // 分页加载的事件数据
      eventsByType: {
        all: [],
        message: [],
        user: [],
        approval: [],
        contact: [],
        group: [],
        document: [],
        calendar: []
      },

      // 分页相关 - 统一的分页状态
      pagination: {
        loading: false,
        noMore: false,
        currentPage: 1,
        pageSize: 10
      },

      // 各类型连接器数据（用于分页）
      officialConnectors: [],
      thirdPartyConnectors: [],
      customConnectors: [],

      // 事件分页相关
      eventPagination: {
        loading: false,
        noMore: false,
        currentPage: 1,
        pageSize: 8
      },

      // 触发事件详情相关
      eventDetailLoading: false,
      selectedEventDetail: null,

      // 第三步：凭证相关
      credentialList: [],
      selectedCredential: null,
      credentialLoading: false
    };
  },

  computed: {
    // 当前显示的事件列表
    currentEvents() {
      if (!this.selectedConnector) return [];

      // 使用分页加载的事件数据
      return this.eventsByType[this.activeEventTab] || [];
    },
    showCredential() {
      return (
        this.selectedConnector &&
        this.selectedConnector.platformType &&
        !isBossDomain()
      );
    }
  },
  methods: {
    isBossDomain,
    // 流程引擎规范 - 初始化节点数据
    setData(data) {
      if (data) {
        this.nodeId = data.id || '';
        this.nodeName = data.nodeName || '';
        this.subNodeReqList = data.subNodeReqList || [];
        this.refData = data.refData || [];

        // 解析任务数据，恢复选中状态
        if (data.taskData && data.taskData.extension) {
          this.taskData = data.taskData;
          const extension = data.taskData.extension;

          // 恢复连接器选择
          if (extension.connectorId) {
            this.selectedConnector = {
              id: extension.connectorId,
              name: extension.connectorName,
              platformType: extension.platformType
            };
            this.activeConnectorTab =
              extension.activeConnectorTab || 'official';
            this.generateEventTabs(this.selectedConnector);
            if (extension.scheduleCron) {
              this.scheduleCron = extension.scheduleCron;
            }
          }

          // 恢复事件选择
          if (
            (extension.eventId || this.scheduleCron) &&
            this.selectedConnector
          ) {
            this.selectedEvent = {
              id: extension.eventId,
              name: extension.eventName
            };
            this.currentStep = 2;
            // 重置事件分页状态并加载初始数据
            this.resetEventPagination();
            this.loadMoreEvents();
            // 恢复事件详情
            this.loadEventDetail(this.selectedEvent);
          }

          // 恢复凭证配置
          if (extension.credentialId && extension.credentialName) {
            this.selectedCredential = {
              id: extension.credentialId,
              name: extension.credentialName
            };
            this.currentStep = 3;
            this.loadCredentialList();
          }
        }
      }
    },

    // 流程引擎规范 - 获取节点任务内容
    getData() {
      const nodeLabel = [];
      let taskData = {
        extension: {},
        inputs: [], // 下个节点可引用变量列表
        preload: {}
      };

      if (this.selectedEventDetail) {
        // 设置入参配置, 需要校验id
        taskData.inputs = this.inputsAdapter(
          this.selectedEventDetail.params || []
        );
      }

      if (this.selectedConnector) {
        taskData.extension.connectorId = this.selectedConnector.id;
        taskData.extension.connectorName = this.selectedConnector.name;
        taskData.extension.platformType = this.selectedConnector.platformType;
        taskData.extension.activeConnectorTab = this.activeConnectorTab;
        nodeLabel.push({
          label: '连接器',
          value: this.selectedConnector.name
        });
        if (this.scheduleCron) {
          taskData.extension.scheduleCron = this.scheduleCron;
        }
      }

      if (this.selectedEvent) {
        taskData.extension.eventId = this.selectedEvent.id;
        taskData.extension.eventName = this.selectedEvent.name;
        taskData.extension.eventGroup = this.selectedEvent.group;
        nodeLabel.push({
          label: '触发事件',
          value: this.selectedEvent.name
        });

        // 保存事件详情数据
        if (this.selectedEventDetail) {
          taskData.extension.eventDetail = this.selectedEventDetail;
        }
      }
      if (this.selectedCredential) {
        taskData.extension.credentialId = this.selectedCredential.id;
        taskData.extension.credentialName = this.selectedCredential.name;
      }

      // 设置节点标签，用于画布显示
      taskData.extension.nodeLabel = nodeLabel;
      this.saveTrigger();
      return {
        taskData
      };
    },
    beforeActiveChange(cb, index) {
      if (index === 0) {
        this.currentStep = index + 1;
        cb();
      } else if (index === 1 && this.selectedConnector) {
        this.currentStep = index + 1;
        cb();
      } else if (
        index === 2 &&
        this.selectedConnector &&
        (this.selectedEvent || this.scheduleCron)
      ) {
        this.currentStep = index + 1;
        cb();
      }
    },
    saveTrigger() {
      const id = this.$route.params.id;
      const triggerEventId = this.selectedEvent && this.selectedEvent.id;
      const triggerCredentialId =
        this.selectedCredential && this.selectedCredential.id;
      if (id && (triggerEventId || this.scheduleCron)) {
        const api = isBossDomain()
          ? ipaasService.updateTempFlow
          : ipaasService.updateFlow;
        api(id, {
          triggerEventId,
          scheduleCron: this.scheduleCron,
          triggerCredentialId
        });
      }
    },
    inputsAdapter(params = []) {
      // paramType到fieldType的映射表
      const typeMap = {
        string: 1,
        number: 2,
        boolean: 4,
        object: 5,
        array_string: 6,
        array_number: 7,
        array_boolean: 7,
        array_object: 9
      };

      // 递归转换参数列表
      const convertParams = paramList => {
        if (!Array.isArray(paramList)) {
          return [];
        }

        return paramList.map(param => {
          // 基本字段映射
          const converted = {
            id: param.id || '',
            required: param.required || false,
            fieldType: typeMap[param.paramType] || 1, // 默认为string类型
            field: param.paramKey || '',
            desc: param.paramName || '',
            properties: []
          };

          // 递归处理properties子类型
          if (
            param.properties &&
            Array.isArray(param.properties) &&
            param.properties.length > 0
          ) {
            converted.properties = convertParams(param.properties);
          }

          return converted;
        });
      };

      return convertParams(params);
    },

    // 流程引擎规范 - 获取校验结果
    validate() {
      // 必须选择连接器和触发事件
      if (!this.selectedConnector) {
        this.$message.warning('请选择设置器');
        return false;
      }
      if (
        this.selectedConnector &&
        this.selectedConnector.id === '-1' &&
        !this.scheduleCron
      ) {
        this.$message.warning('请填写Cron表达式');
        return false;
      }

      if (!this.selectedEvent && this.selectedConnector.id !== '-1') {
        this.$message.warning('请选择触发事件');
        return false;
      }

      if (
        this.selectedConnector.platformType &&
        !isBossDomain() &&
        !this.selectedCredential
      ) {
        this.$message.warning('请选择凭证');
        return false;
      }

      return true;
    },

    // 选择连接器
    selectConnector(connector, changed) {
      if (changed) {
        // 清空之前选择的事件
        this.selectedEvent = null;
        this.scheduleCron = '';
        this.selectedCredential = null;
      }
      this.selectedConnector = connector;
      this.nextStep();
    },

    // 下一步
    async nextStep() {
      if (this.selectedConnector && this.currentStep === 1) {
        this.currentStep = 2;
        if (this.selectedConnector.id === '-1') return;

        // 生成事件分组tabs（异步操作）
        await this.generateEventTabs(this.selectedConnector);

        // 重置事件分页状态并加载初始数据
        this.resetEventPagination();
        this.loadMoreEvents();
      } else if (
        this.selectedEvent &&
        this.currentStep === 2 &&
        this.showCredential
      ) {
        this.currentStep = 3;
        this.loadCredentialList();
      }
    },

    // 上一步
    prevStep() {
      this.currentStep = 1;
      // this.selectedEvent = null;
    },

    // 处理事件Tab点击
    handleEventTabClick(tab) {
      this.activeEventTab = tab.name;

      // 切换tab时重置分页状态并加载数据
      this.resetEventPagination();
      this.loadMoreEvents();
    },

    // 选择事件
    async selectEvent(event) {
      if (this.selectedEvent && event.id === this.selectedEvent.id) {
        if (!isBossDomain()) {
          this.nextStep();
        }
        return;
      }
      this.selectedEvent = event;

      // 清空之前的详情数据
      this.selectedEventDetail = null;
      this.selectedCredential = null;

      // 获取触发事件详情
      if (this.selectedConnector && event) {
        await this.loadEventDetail(event);
      }
      if (!isBossDomain()) {
        this.nextStep();
      }
    },
    // 凭证变化处理
    selectCredential(credential) {
      this.selectedCredential = credential;
    },

    // 加载触发事件详情
    async loadEventDetail(event) {
      if (this.selectedConnector.id === '-1') return;
      this.eventDetailLoading = true;
      try {
        const response = await ipaasService.getTriggerEventDetail(
          this.selectedConnector.id,
          event.id
        );

        if (response) {
          // 恢复id
          const inputs = this.taskData && this.taskData.inputs;
          const evId =
            this.taskData &&
            this.taskData.extension &&
            this.taskData &&
            this.taskData.extension.eventId;
          if (inputs && evId === event.id) {
            // paramType到fieldType的映射表
            const typeMap = {
              string: 1,
              number: 2,
              boolean: 4,
              object: 5,
              array_string: 6,
              array_number: 7,
              array_object: 9
            };
            const filterItem = (list, oglist) => {
              return list.map(item => {
                const tg = oglist.find(
                  i =>
                    i.fieldType === typeMap[item.paramType] &&
                    item.field === i.paramKey
                );
                if (tg) {
                  item.id = tg.id;
                  if (item.properties) {
                    item.properties = filterItem(
                      item.properties,
                      tg.properties
                    );
                  }
                }
                return item;
              });
            };
            response.params = filterItem(response.params, inputs);
          }
          this.selectedEventDetail = response;
          console.log('触发事件详情:', response);
        } else {
          this.selectedEventDetail = null;
          console.warn('获取触发事件详情返回空数据');
        }
      } catch (error) {
        console.error('获取触发事件详情失败:', error);
        this.selectedEventDetail = null;
        this.$message &&
          this.$message.error('获取触发事件详情失败，请稍后重试');
      } finally {
        this.eventDetailLoading = false;
      }
    },

    // 加载凭证列表
    async loadCredentialList() {
      if (!this.selectedConnector) return;

      this.credentialLoading = true;
      try {
        const response = await ipaasService.getConnectorCredentials(
          this.selectedConnector.id,
          'trigger'
        );

        if (response && Array.isArray(response)) {
          this.credentialList = response;
        } else {
          this.credentialList = [];
        }
      } catch (error) {
        console.error('加载凭证列表失败:', error);
        this.$message.error('加载凭证列表失败');
        this.credentialList = [];
      } finally {
        this.credentialLoading = false;
      }
    },

    // 确认选择
    confirmSelection() {
      if (this.selectedConnector && this.selectedEvent) {
        // const result = {
        //   connector: this.selectedConnector,
        //   event: this.selectedEvent,
        //   eventDetail: this.selectedEventDetail
        // };

        this.$emit('confirm', this.getData());
      }
    },

    // 根据连接器生成事件分组tabs
    async generateEventTabs(connector) {
      if (connector.id === '-1') return;
      if (!connector) {
        this.eventTabs = [{ key: 'all', label: '全部' }];
        return;
      }

      try {
        // 获取连接器的分组信息
        const response = await ipaasService.getConnectorGroups(connector.id);

        if (response && response.length > 0) {
          // 根据分组生成tabs
          this.eventTabs = [
            { key: 'all', label: '全部' },
            ...response.map(group => ({
              key: group.id.toString(),
              label: group.name
            }))
          ];
        } else {
          // 没有分组时只显示全部
          this.eventTabs = [{ key: 'all', label: '全部' }];
        }
      } catch (error) {
        console.error('获取连接器分组失败:', error);
        // 发生错误时使用默认的tabs
        this.eventTabs = [{ key: 'all', label: '全部' }];
      }

      this.activeEventTab = 'all';
    },
    // 处理事件滚动
    handleEventScroll(event) {
      const target = event.target;
      const scrollTop = target.scrollTop;
      const scrollHeight = target.scrollHeight;
      const clientHeight = target.clientHeight;

      // 滚动到底部时触发加载
      if (
        scrollTop + clientHeight >= scrollHeight - 10 &&
        !this.eventPagination.loading &&
        !this.eventPagination.noMore
      ) {
        this.loadMoreEvents(true);
      }
    },

    // 加载更多事件数据
    async loadMoreEvents(append = false) {
      if (this.selectedConnector.id === '-1') return;
      if (
        this.eventPagination.loading ||
        this.eventPagination.noMore ||
        !this.selectedConnector
      ) {
        return;
      }

      this.eventPagination.loading = true;

      try {
        // 构建请求参数
        const requestData = {};

        // 如果不是"全部"tab，则按分组过滤
        if (this.activeEventTab !== 'all') {
          requestData.groupId = this.activeEventTab;
        }

        const params = {
          offset:
            (this.eventPagination.currentPage - 1) *
            this.eventPagination.pageSize,
          limit: this.eventPagination.pageSize,
          direction: 'asc'
        };

        // 调用触发事件分页查询接口
        const response = await ipaasService.getTriggerEventList(
          this.selectedConnector.id,
          requestData,
          params
        );

        // 检查响应数据
        if (response && response.datas && Array.isArray(response.datas)) {
          const dataList = response.datas;

          // 判断是否为最后一页
          const isLastPage =
            dataList.length < this.eventPagination.pageSize ||
            (response.paging &&
              response.paging.offset + response.paging.limit >=
              response.paging.count);

          if (dataList.length > 0) {
            // 有数据时更新列表
            if (append) {
              this.appendEventData(dataList);
            } else {
              this.setEventData(dataList);
            }

            // 更新分页状态
            this.eventPagination.currentPage++;
          } else {
            // 第一页就没有数据的情况
            if (!append) {
              this.setEventData([]);
            }
          }

          this.eventPagination.noMore = isLastPage;
        } else {
          // API返回格式不正确或没有数据
          console.warn('触发事件API返回数据格式异常:', response);

          if (!append) {
            this.setEventData([]);
          }
          this.eventPagination.noMore = true;
        }
      } catch (error) {
        console.error('加载事件失败:', error);

        // 发生错误时也要清空数据（如果不是追加模式）
        if (!append) {
          this.setEventData([]);
        }
        this.eventPagination.noMore = true;

        // 显示错误提示
        this.$message &&
          this.$message.error(
            error.message || '加载触发事件数据失败，请稍后重试'
          );
      } finally {
        // 确保loading状态总是被重置
        this.eventPagination.loading = false;
      }
    },

    // 设置事件数据
    setEventData(list) {
      this.eventsByType[this.activeEventTab] = list;
    },

    // 追加事件数据
    appendEventData(list) {
      this.eventsByType[this.activeEventTab].push(...list);
    },

    // 重置事件分页状态
    resetEventPagination() {
      this.eventPagination.currentPage = 1;
      this.eventPagination.loading = false;
      this.eventPagination.noMore = false;
    }
  }
};
</script>
