import {
  bpaasBaseApi
} from 'yxt-biz-pc/packages/api';

const BASE_URL = '/intghub';

/**
 * iPaaS 集成平台接口服务
 */
export default {
  /**
   * 分页查询连接流列表
   * @param {Object} data - 查询参数
   * @param {string} data.name - 连接流名称(模糊查询)
   * @param {string} data.templateId - 所属模板ID
   * @param {string} data.triggerEventId - 触发事件ID
   * @param {number} data.status - 状态(0:草稿, 1:已发布)
   * @param {number} data.enableStatus - 启用状态(0:禁用, 1:启用)
   * @param {Object} params - 分页参数
   * @param {number} params.offset - 从第几条开始取 默认0
   * @param {number} params.limit - 每页多少条 默认20
   * @param {string} params.direction - 创建时间排序规则,asc,desc,默认asc
   * @returns {Promise}
   */
  getFlowList(data = {}, params = {}) {
    return bpaasBaseApi({
      url: `${BASE_URL}/flows/page`,
      method: 'post',
      data,
      params: {
        offset: 0,
        limit: 20,
        direction: 'asc',
        ...params
      }
    });
  },

  /**
   * 查询连接器详情
   * @param {string} id - 连接器ID
   * @returns {Promise}
   */
  getConnectorDetail(id) {
    return bpaasBaseApi({
      url: `${BASE_URL}/connectors/${id}`,
      method: 'get'
    });
  },

  /**
   * 分页查询连接器列表
   * @param {Object} data - 查询参数
   * @param {string} data.name - 连接器名称(模糊查询)
   * @param {string} data.code - 连接器编码(模糊查询)
   * @param {number} data.type - 连接器类型(0:官方连接器, 1:三方连接器, 2:自建连接器)
   * @param {Object} params - 分页参数
   * @param {number} params.offset - 从第几条开始取 默认0
   * @param {number} params.limit - 每页多少条 默认20
   * @param {string} params.direction - 创建时间排序规则,asc,desc,默认asc
   * @returns {Promise}
   */
  getConnectorList(data = {}, params = {}) {
    return bpaasBaseApi({
      url: `${BASE_URL}/connectors/page`,
      method: 'post',
      data,
      params: {
        offset: 0,
        limit: 20,
        direction: 'asc',
        ...params
      }
    });
  },

  /**
   * 查询指定连接器下所有分组（不分页）
   * @param {string} connectorId - 连接器ID
   * @returns {Promise}
   */
  getConnectorGroups(connectorId) {
    return bpaasBaseApi({
      url: `${BASE_URL}/connectors/${connectorId}/groups/list`,
      method: 'get'
    });
  },

  /**
   * 分页查询触发事件列表
   * @param {string} connectorId - 连接器ID
   * @param {Object} data - 查询参数
   * @param {string} data.name - 事件名称(模糊查询)
   * @param {string} data.code - 事件编码(模糊查询)
   * @param {string} data.groupId - 所属分组ID
   * @param {number} data.status - 状态(0:草稿, 1:已发布)
   * @param {Object} params - 分页参数
   * @param {number} params.offset - 从第几条开始取 默认0
   * @param {number} params.limit - 每页多少条 默认20
   * @param {string} params.direction - 创建时间排序规则,asc,desc,默认asc
   * @returns {Promise}
   */
  getTriggerEventList(connectorId, data = {}, params = {}) {
    return bpaasBaseApi({
      url: `${BASE_URL}/connectors/${connectorId}/events/page`,
      method: 'post',
      data,
      params: {
        offset: 0,
        limit: 20,
        direction: 'asc',
        ...params
      }
    });
  },

  /**
   * 查询触发事件详情
   * @param {string} connectorId - 连接器ID
   * @param {string} id - 触发事件ID
   * @returns {Promise}
   */
  getTriggerEventDetail(connectorId, id) {
    return bpaasBaseApi({
      url: `${BASE_URL}/connectors/${connectorId}/events/${id}`,
      method: 'get'
    });
  },

  /**
   * 分页查询执行动作列表
   * @param {string} connectorId - 连接器ID
   * @param {Object} data - 查询参数
   * @param {string} data.name - 动作名称(模糊查询)
   * @param {string} data.code - 动作编码(模糊查询)
   * @param {string} data.groupId - 所属分组ID
   * @param {number} data.status - 状态(0:草稿, 1:已发布)
   * @param {Object} params - 分页参数
   * @param {number} params.offset - 从第几条开始取 默认0
   * @param {number} params.limit - 每页多少条 默认20
   * @param {string} params.direction - 创建时间排序规则,asc,desc,默认asc
   * @returns {Promise}
   */
  getActionList(connectorId, data = {}, params = {}) {
    return bpaasBaseApi({
      url: `${BASE_URL}/connectors/${connectorId}/actions/page`,
      method: 'post',
      data,
      params: {
        offset: 0,
        limit: 20,
        direction: 'asc',
        ...params
      }
    });
  },

  /**
   * 查询执行动作详情
   * @param {string} connectorId - 连接器ID
   * @param {string} id - 执行动作ID
   * @returns {Promise}
   */
  getActionDetail(connectorId, id) {
    return bpaasBaseApi({
      url: `${BASE_URL}/connectors/${connectorId}/actions/${id}`,
      method: 'get'
    });
  },

  /**
   * 查询指定连接器下所有凭证（只返回id和name，不分页）
   * @param {string} connectorId - 连接器ID
   * @returns {Promise}
   */
  getConnectorCredentials(connectorId, credentialType = 'action') {
    return bpaasBaseApi({
      url: `${BASE_URL}/connectors/${connectorId}/credentials/simple?credentialType=${credentialType}`,
      method: 'get'
    });
  },

  updateTempFlow(flowId, data) {
    return bpaasBaseApi({
      url: `${BASE_URL}/flow/templates/${flowId}`,
      method: 'put',
      data
    });
  },

  updateFlow(flowId, data) {
    return bpaasBaseApi({
      url: `${BASE_URL}/flows/${flowId}`,
      method: 'put',
      data
    });
  }
};
