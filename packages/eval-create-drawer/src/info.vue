<!-- 设置方案 -->
<template>
  <div class="gwnl-position-eval__info pb40">
    <yxt-form ref="form" v-loading="loading" :rules="rules" :model="form" label-width="100px" label-position="top"
              is-need-require>
      <yxt-blockhead :title="$t('pc_base_info')" size="small" type="special" />
      <div class="ml16 mt8">
        <yxt-form-item :label="$t('pc_gwnl_global_lbl_projectName')" prop="name">
          <yxt-input v-model="form.name" maxlength="50" :placeholder="$t('pc_gwnl_global_enterProjectName')"
                     show-word-limit />
        </yxt-form-item>
        <!-- 再次渲染，防止样式收缩成+n的形式 -->
        <yxt-form-item v-if="showInfo" :label="$t('pc_eval_lbl_project_owner').d('项目负责人')" prop="owners">
          <yxt-select v-model="form.owners" multiple :placeholder="$t('pc_gwnl_global_please_selected').d('请选择')"
                      icon-name="more" hidden-option collapse-tags value-key="id" prop-label="fullname" clearable
                      style="width: 100%;" @visible-change="handleOpenOwner">
            <template slot="tag" slot-scope="{ data }">
              <span>{{ data.currentLabel }}</span>
            </template>
          </yxt-select>
        </yxt-form-item>
        <yxt-form-item v-if="!isBehavior" prop="targetId" :label="$t('pc_gwnl_eval_evalTools')">
          <span>{{ selectedUtil && selectedUtil.surveyName }}</span>
        </yxt-form-item>

        <yxt-form-item v-if='isPerson && selectedUtil.packageType !== EnumPackageType.beizhi'
                       :label="$t('pc_gwnl_eval_lbl_evalNorm')" prop='modelId'>
          <yxt-select v-model='form.modelId' :placeholder="$t('pc_gwnl_global_pleaseSelect')" style="width: 100%;"
                      @change='changeNorms'>
            <yxt-option v-for='norm in selectedUtil.normList' :key='norm.normId' :label='norm.normName'
                        :value='norm.normId' />
          </yxt-select>
        </yxt-form-item>
        <yxt-form-item :label="$t('pc_gwnl_eval_lbl_evalTime')" prop='time'>
          <yxt-date-picker v-model='form.time' style="width: 100%;" value-format="yyyy-MM-dd HH:mm:ss"
                           format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']" type='datetimerange'
                           :range-separator="$t('pc_gwnl_eval_to')" :start-placeholder="$t('pc_gwnl_global_startDateTime')"
                           :end-placeholder="$t('pc_gwnl_global_endDateTime')" :picker-options='pickerOptions' />
        </yxt-form-item>
        <yxt-form-item :label="$t('pc_gwnl_web_lbl_survey_information_hint')" prop="description">
          <yxt-input v-model="form.description" type="textarea" :rows="3" maxlength="1000"
                     :placeholder="$t('pc_gwnl_web_pleaseEnter') + $t('pc_gwnl_web_lbl_survey_information_hint')"
                     show-word-limit />
        </yxt-form-item>
      </div>
      <template v-if="peerAssess || isBehavior">
        <yxt-blockhead class="mt32 mb8" :title="$t('pc_gwnl_eval_evalSettings').d('评估设置')" size="small"
                       type="special" />
        <div class="ml16">
          <yxt-form-item :label="$t('pc_gwnl_eval_evalRelationship')" required prop='evaluationConfig4Create'>
            <div v-if="showInfo" class='flex-col flex' :class="{ 'eval-custom-checkbox': !expend && isBehavior }">
              <div v-for="item in dimensionInfo" :key="item.type">
                <!-- 其他评估关系 -->
                <yxt-checkbox v-if="item.type !== '2'" v-model='form.evaluationConfig4Create[item.prop]' :true-label='1'
                              :false-label='0' :disabled="disabledRelationBox(item)"
                              @change="changeEnable(form.evaluationConfig4Create[item.prop], item.type)">
                  <!-- 最多支持6个评估关系 -->
                  <eval-tooltip :label="item.label" :disabledFunc="disabledRelationBox(item)" />
                </yxt-checkbox>
                <!-- 上级评估 -->
                <template v-else>
                  <yxt-checkbox v-model='form.evaluationConfig4Create.enabledLeader' :true-label='1' :false-label='0'
                                :disabled="disabledRelationBox(item)" @change='changeLeaderEnable'>
                    <!-- 最多支持6个评估关系 -->
                    <eval-tooltip :label="$t('pc_gwnl_global_leaderAssessment')"
                                  :disabledFunc="disabledRelationBox(item)" />
                  </yxt-checkbox>
                  <template v-if='form.evaluationConfig4Create.enabledLeader === 1'>
                    <yxt-radio v-model="leaderType" label="enabledLeaderPm" @change="changeLeaderType">
                      {{ $t('pc_gwnl_eval_departmentManager') }}
                    </yxt-radio>
                    <yxt-radio v-model="leaderType" label="enabledLeaderDm" @change="changeLeaderType">
                      {{ $t('pc_gwnl_eval_directManager') }}
                    </yxt-radio>
                  </template>
                </template>
              </div>
            </div>
            <!-- 行为评估才展示 -->
            <div v-if="isBehavior && dimensionInfo && dimensionInfo.length > 6" class="eval-custom-text hand"
                 @click="expend = !expend">
              <span class="color-primary-6">{{ expend ? $t('pc_gwnl_clamp_retract' /* 收起 */) : $t('pc_comp_sideBar_open'
                /* 展开 */)
                }}</span>
              <yxt-svg width="16px" height="16px" :icon-class="expend ? 'arrow_down' : 'arrow_up'"
                       class="color-primary-6 ml2" />
            </div>
          </yxt-form-item>
          <template v-if='weightList && weightList.length > 1'>
            <yxt-form-item :label="$t('pc_gwnl_eval_lbl_evalWeight')" prop='weight' required
                           :label-tooltip="$t('pc_gwnl_eval_weightSettingTooltip')">
              <div class='flex flex-col'>
                <div class='mt8 lh24'>
                  <yxt-radio v-model='form.evaluationConfig4Create.weightType' :label='1' @change='setAverageWeight'>
                    {{ $t('pc_gwnl_eval_averageWeight') }}
                  </yxt-radio>
                </div>
                <div class='mt8'>
                  <yxt-radio v-model='form.evaluationConfig4Create.weightType' :label='2' @change='setAverageWeight'>
                    {{ $t('pc_gwnl_eval_customWeight') }}
                  </yxt-radio>
                  <div v-if='parseInt(form.evaluationConfig4Create.weightType) === 2'
                       class="flex flex-wrap mt12 bg-lightgrey pl16 pt16 pb8 yxtbizf-br-4">
                    <div v-for='item in weightList' :key='item.weight' class='flex weight-item mb8'>
                      <yxt-tooltip :content="item.label" placement="top" open-filter>
                        <span class='mr8 ellipsis'>{{ item.label }}</span>
                      </yxt-tooltip>
                      <yxt-input-number v-model='weight[item.weight]' class="yxtbiz-flex-shrink-0" :precision='2'
                                        :min='0' :max='100' />
                      <span class="ml8">%</span>
                    </div>
                  </div>
                </div>
              </div>
            </yxt-form-item>
            <yxt-form-item :label="$t('pc_talent_lbl_other_eval_rule').d('他评分计算规则')"
                           prop='evaluationConfig4Create.othersWeightType' required>
              <yxt-radio-group v-model="form.evaluationConfig4Create.othersWeightType">
                <div>
                  <yxt-radio :label="0">
                    {{ $t('pc_talent_msg_use_weight' /* 权重计算 */) }}

                    <eval-tooltip-svg svg-class="pb2 v-mid" is-use-slot-title slot-title="pc_biz_eval_msg_weight_radio"
                                      :max-height="300" />
                  </yxt-radio>
                </div>
                <div class="mt16">
                  <div class="eval-self-weight-left">
                    <yxt-radio :label="1">
                      {{ $t('pc_talent_msg_eval_not_use_weight' /* 非权重计算 */) }}

                      <eval-tooltip-svg svg-class="pb2 v-mid" is-use-slot-title
                                        slot-title="pc_biz_eval_msg_no_weight_radio" :max-height="300" />
                    </yxt-radio>
                  </div>
                </div>
              </yxt-radio-group>
            </yxt-form-item>
          </template>
          <yxt-form-item v-if="type === 4" size="small" :label="$t('pc_talent_lbl_eval_content').d('评估内容')">
            <yxt-switch v-model="form.isImport" :active-value='1' :inactive-value='0' />
            <span class="ml4">{{ $t('pc_talent_msg_enable_eval_data_import').d('支持初始化数据导入') }}</span>
          </yxt-form-item>
        </div>
      </template>

      <!-- 作答设置 -->
      <template v-if="isBehavior">
        <yxt-blockhead class="mt32 mb16" :title="$t('pc_biz_survey_lbl_answer_set')" size="small" type="special" />

        <div class="ml16 mb24 eval-answer-set">
          <template v-if="isBehavior">
            <yxt-switch v-if="form.evaluationConfig4Create.enabledSelf" class="mb16" v-model="showStandard"
                        :active-text="$t('pc_eval_lbl_self_evaluation_display_standard_label' /* 自评时显示【标准】标识 */)"></yxt-switch>
            <div class="yxtbiz-flex-center">
              <yxt-switch v-model="showSupportSwitch"
                          :active-text="$t('pc_biz_eval_lbl_auto_switch' /* 自动翻页 */)"></yxt-switch>

              <eval-tooltip-svg :content="$t('pc_biz_eval_lbl_auto_switch_tips')" />
            </div>
          </template>
        </div>
      </template>
      <template v-if="isBeiEval">
        <yxtbiz-ai-answer-setting
                                ref="setting"
                                :evalId='id'
                                :toolId='selectedUtil.fbiPrjId'/>
      </template>


      <div class="ml16">
        <yxt-form-item :label="$t('pc_gwnl_global_remarks')" prop='remark'>
          <yxt-input v-model='form.remark' type='textarea' :rows='4' maxlength='200'
                     :placeholder="$t('pc_gwnl_eval_enterRemark')" show-word-limit />
        </yxt-form-item>
        <yxt-form-item class="eval-remark1" v-if="!isBehavior">
          <yxt-checkbox v-model="form.autoIssueReport" :true-label="1" :false-label="0" @change="msgTypeChange">
            <!-- 报告生成后自动下发给学员 -->
            {{ $t('pc_eval_eval_seting_tips') }}
          </yxt-checkbox>
        </yxt-form-item>
      </div>
    </yxt-form>
    <yxtbiz-eval-step-footer :operation='operation' footer-width='960px' />
    <yxtbiz-eval-select-user ref="userselector" :function-code="functionCode" :data-permission-code="dataPermissionCode"
                             :data="form.owners" :visible.sync="selectUserVisible" :tabs="['persons', 'userGroup', 'advance']" :limit="1000"
                             @getUsers="getUsers">
    </yxtbiz-eval-select-user>
  </div>
</template>

<script>
import dayjs from 'dayjs';
import { getEvalData, getSurveyById, saveEval, postAfterEvaluation } from './service.js';
import evalSelectUser from './components/eval-select-user';
import infoMixins from './mixins/info';
import yxtbizAiAnswerSetting from 'packages/ai-answer-setting';

const OPERATION_MAP = new Map([
  [1, 'disabled'],
  [2, 'prevDisabled'],
  [3, 'nextDisabled'],
  [4, 'publishDisabled']
]);

const EnumPackageType = {
  0: 'iselect',
  1: 'beizhi',
  iselect: 0,
  beizhi: 1
};

export default {
  components: {
    yxtbizEvalSelectUser: evalSelectUser,
    yxtbizAiAnswerSetting
  },
  mixins: [infoMixins],
  props: {
    purposeId: {
      type: String,
      default: ''
    },
    // 评估方案场景(1-胜任力 2-人格 3-通用)
    scene: {
      type: Number,
      default: 1
    },
    surveyId: {
      type: String,
      default: ''
    },
    // 评估类型 1：行为评估，2：问卷评估
    type: {
      type: Number,
      default: 2
    },
    // 行为评估指标id列表，仅在行为评估或多次行为评估生效
    indicatorIds: {
      type: Array,
      default: ([])
    },
    indicators: {
      type: Array,
      default: ([])
    },
    owners: {
      type: Array,
      default: () => []
    },
    dataPermissionCode: {
      type: String,
      default: ''
    },
    functionCode: {
      type: String,
      default: 'sp_gwnl_tmap'
    },
    showInfo: {
      type: Boolean,
      default: false
    },
    // 前测的 id
    beforeId: {
      type: String,
      default: ''
    },
    skillModelId: {
      type: String,
      default: ''
    }
  },

  data() {
    // 校验值是否合法
    const validateSetting = (rule, value, callback) => {
      if (this.weightList && this.weightList.length === 0) {
        callback(
          new Error(this.$t('pc_gwnl_eval_msg_pleaseSetParticipateEval'))
        );
      }

      if (
        this.form.evaluationConfig4Create.enabledLeader === 1 &&
        this.form.evaluationConfig4Create.enabledLeaderDm === 0 &&
        this.form.evaluationConfig4Create.enabledLeaderPm === 0
      ) {
        callback(new Error(this.$t('pc_gwnl_eval_msg_pleaseCheckManager')));
      }
      callback();
    };

    // 默认的评估关系
    this.defaultTypes = [
      'enabledSelf',
      'enabledLeader',
      'enabledEqual',
      'enabledSubordinate'
    ];

    const countdownOptions = [
      { label: this.$t('pc_tlive_anyMinute', [1]), value: 1 },
      { label: this.$t('pc_tlive_anyMinute', [2]), value: 2 },
      { label: this.$t('pc_tlive_anyMinute', [3]), value: 3 },
      { label: this.$t('pc_tlive_anyMinute', [4]), value: 4 },
      { label: this.$t('pc_tlive_anyMinute', [5]), value: 5 }
    ];

    return {
      EnumPackageType,
      countdownOptions,
      surveyType: '',
      form: {
        autoIssueReport: 0,
        name: '',
        type: this.type,
        target: {},
        targetId: '',
        targetName: '',
        time: null,
        description: '',
        evaluationConfig4Create: {
          enabledSelf: 0,
          enabledEqual: 0,
          enabledSubordinate: 0, // 开启下级评价
          enabledLeader: 0, // 开启上级评价
          enabledLeaderPm: 0, // 上级部门经理评价
          enabledLeaderDm: 0, // 上级的直属经理评价
          weightType: 1, // 权重类型(1-平均权重,2- 自定义权重)
          othersWeightType: 0, // 他评规则(0-权重计算,1-非权重)
          customEvalConfigs: [],
          selfSwitch: 1, // 自评标准开关
          autoFlipSwitch: 0 // 行为评估下自动选项翻页开关
        },
        remark: '',
        modelId: '',
        modelname: '',
        weight: {},
        scene: 3,
        owners: [],
        chargeSurveyId: '', // 收费问卷id
        isImport: 0, // 初始化数据导入
        indicatorIds: []
      },
      rules: {
        name: [
          {
            required: true,
            message: this.$t('pc_gwnl_eval_fillEvalName'),
            trigger: 'blur'
          },
          {
            max: 50,
            message: this.$t('pc_gwnl_eval_lengthWithin50'),
            trigger: 'blur'
          }
        ],
        owners: [
          { required: true, validator: this.validateUsers, trigger: 'change' }
        ],
        time: [
          { required: true, validator: this.validateTime, trigger: 'change' }
        ],
        description: [
          {
            max: 1000,
            message: this.$t('pc_eval_lbl_lengthWithin_number', [1000]),
            trigger: 'blur'
          }
        ],
        remark: [
          {
            max: 200,
            message: this.$t('pc_gwnl_eval_lengthWithin200'),
            trigger: 'blur'
          }
        ],
        evaluationConfig4Create: [{ validator: validateSetting }],
        weight: [{ validator: this.validateWeight }],
        modelId: [
          {
            required: true,
            message: this.$t('pc_gwnl_eval_msg_pleaseSelectEvalNorm'),
            trigger: 'changed'
          }
        ]
      },
      pickerOptions: {
        disabledDate: time => {
          return time.getTime() < dayjs().startOf('day');
        }
      },
      selectedUtil: null, // 当前选中的问卷
      operation: {
        disabled: false,
        nextDisabled: false,
        next: this.next,
        prev: this.prev,
        save: this.save,
        saveName: this.$t('pc_gwnl_global_btn_saveDraft' /* 保存草稿 */)
      },
      surveyChanged: false, // 问卷工具是否发生了变化
      defaultDimensionInfo: [{ type: '1' }, { type: '2' }, { type: '3' }, { type: '4' }],
      peerAssess: 0, // 是否支持他评
      selfAssess: 1, // 是否支持自评
      selectUserVisible: false,
      oldSurveyId: '', // 老的问卷id
      oldSurveyUtil: {}, // 前测对应的评估关系数据
      oldWeightType: 1, // 前测对应的权重类型(1-平均权重,2- 自定义权重)
      oldEvaluationConfig: {}, // 切换评估关系后再次切回相同工具或者行为时的缓存
      firstCreateEval: 1 // 是否第一次进入创建测评
    };
  },
  computed: {
    isPerson() {
      // 问卷类型：0.通用，1.人格
      return this.surveyType === 1;
    },
    dimensionInfo() {
      return this.selectedUtil ? this.selectedUtil.dimensionInfoBeans : this.defaultDimensionInfo;
    },
    isBehavior() {
      return [1, 4].includes(this.form.type);
    },
    bfId() {
      return this.beforeId && !this.id;
    },

    // 是否是BEI测评
    isBeiEval() {
      return this.selectedUtil && (this.selectedUtil.surveySource === 4 || this.selectedUtil.surveySource === 5);
    }
  },
  watch: {
    stepsData: {
      immediate: true,
      handler(data) {
        if (data[0] === 'info') {
          this.operation.prev = false;
        }
      }
    },
    'surveyId': {
      // 问卷工具发生了变化，需要重新获取信息，或者清空当前页面信息
      immediate: true,
      handler(value) {
        this.surveyChanged = true;
      }
    },
    // 创建行为评估时获取后端的接口
    showInfo(v) {
      v && this.getIdBehaviorList();
    }
  },
  created() {
    this.getEvalInfo();
  },
  methods: {
    async getIdBehaviorList(val = false) {
      if (!this.isBehavior) return;

      // 如果有oldSurveyId则说明之前是工具类型，切换成了行为评估
      const serverId = this.oldSurveyId ? '' : (this.id || '');
      await this.getBehaviorList(serverId);

      if (!val) {
        // 行为评估类型 & 之前是行为评估创建好的
        if ([1, 4].includes(this.type) && this.id && !this.oldSurveyId) {
          const result = await getEvalData(this.id);
          // 如果没有targetId说明没有切换成工具类型，此时需要获取之前的配置
          // 因为resetData是不清空数据的，所以如果修改了信息页的数据，点击上一步，再点击下一步数据要重新获取
          if (!result.targetId) {
            // 设置表单基本信息
            this.setFormVal(result);
            // 设置表单评估关系
            this.setFormEva(result);
          } else {
            this.checkedAllDimension();
          }
        } else {
          this.checkedAllDimension();
        }
      }

      if (this.id && !this.oldSurveyId) {
        this.expend = true;
      }
    },

    validateUsers(rule, value, callback) {
      if (this.form.owners && this.form.owners.length) {
        callback();
      } else {
        callback(new Error(this.$t('pc_eval_lbl_choose_projectOwner').d('请选择负责人')));
      }
    },
    validateTime(rule, value, callback) {
      if (!value) {
        callback(new Error(this.$t('pc_gwnl_eval_pleaseSelectEvalTime')));
        return;
      }
      if (value || value.length !== 0) {
        // eslint-disable-next-line no-unused-vars
        let ed = value[1];
        if (dayjs().isAfter(value[1], 'time')) {
          callback(new Error(this.$t('pc_gwnl_eval_closeTime_currentTime')));
        } else {
          callback();
        }
      }
    },
    msgTypeChange() {
      if (this.form.autoIssueReport) {
        this.$confirm(this.$t('pc_gwnl_eval_auto_send_report_tips'), this.$t('pc_gwnl_tips'), {
          type: 'warning',
          showCancelButton: false,
          confirmButtonText: this.$t('pc_gwnl_global_msg_known')
        });
      }
    },
    changeNorms(id) {
      if (id) {
        const cur = this.selectedUtil.normList.filter(item => {
          return item.normId === id;
        });

        this.form.modelName = cur[0].normName;
      }
    },

    // 重新设置表单的各种参数
    async reSetForm() {
      // 如果有id则获取id对应的评估关系配置, 同时还是前测
      if (!this.id) return;

      try {
        this.loading = true;
        const result = await getEvalData(this.id);
        // 设置表单基本信息
        this.setFormVal(result);
        // 设置表单评估关系
        this.setFormEva(result);
        this.loading = false;
      } catch (err) {
        this.loading = false;
        this.handleError(err);
      }
    },

    // 修改了工具后进行重置数据
    resetFormEvaluationConfig() {
      const replaceKey2 = ['enabledSelf', 'enabledEqual', 'enabledSubordinate', 'enabledLeader', 'enabledLeaderPm', 'enabledLeaderDm', 'weightType', 'weightSelf', 'weightLeader', 'weightEqual', 'weightSubordinate', 'othersWeightType', 'autoFlipSwitch'];

      let evaluationConfig = {};
      replaceKey2.forEach((key) => {
        evaluationConfig[key] = 0;
      });

      // 设置部分参数设置项的初始值
      const setting = ['weightType', 'selfSwitch'];
      setting.forEach(key => {
        evaluationConfig[key] = 1;
      });

      // 根据获取最新的配置进行自定义关系的处理
      // 默认的配置在checkedAllDimension进行enable赋值
      evaluationConfig.customEvalConfigs = this.selectedUtil.customDimensionBeans.map(item => {
        return {
          relationType: item.type,
          dimensionName: item.dimensionName,
          weight: 0,
          orderIndex: item.orderIndex
        };
      });

      this.form.evaluationConfig4Create = evaluationConfig;
    },

    // 获取当前问卷信息
    async getCurSurvey(isSelect = true, isInit = false) {
      // 放在这个地方是因为getCurSurvey每次都会调用，而且执行在setEvalData之后
      // 默认初始化存入评估权重，因为选过自评工具所以会变成 1
      // 这个时候如果再选了其他360工具，则要恢复原来前测对应的评估权重
      if (this.bfId) {
        this.form.evaluationConfig4Create.weightType = this.oldWeightType;
      }

      // 只要切换成了行为则就清空selectedUtil
      if (this.isBehavior) {
        this.surveyChanged = false;
        this.selectedUtil = null;
        return;
      }

      if (this.surveyId) {
        this.selectedUtil = await getSurveyById(this.surveyId);
        console.log(this.selectedUtil);
        this.peerAssess = this.selectedUtil.peerAssess;
        this.selfAssess = this.selectedUtil.selfAssess;
        this.surveyType = this.selectedUtil.surveyType;
        // 如果有默认的评估关系 ｜ 自定义的评估关系
        if (this.selectedUtil.dimensionInfoBeans || this.selectedUtil.customDimensionBeans) {
          // 自定义的设置默认为1
          this.selectedUtil.customDimensionBeans.forEach(item => {
            item.label = item.dimensionName;
            item.prop = `enabledCustom${item.type}`;
            item.isDefault = 1;
          });

          // 修改自上评下的 label和 prop
          this.selectedUtil.dimensionInfoBeans.forEach(item => {
            item.label = this.defaultRelation[item.type].checkboxLabel;
            item.prop = this.defaultRelation[item.type].prop;
          });

          this.selectedUtil.dimensionInfoBeans = this.selectedUtil.dimensionInfoBeans.concat(this.selectedUtil.customDimensionBeans).sort((d1, d2) => d1.orderIndex - d2.orderIndex);
        } else {
          this.selectedUtil.dimensionInfoBeans = await this.getRelationCustomList('', 'defaultDimensionInfo');
        }

        // 非前后测关联处理逻辑
        if (!this.bfId) {
          // 此时还未进入到信息页，只在第一步选择工具页面
          if (this.surveyId === this.oldSurveyId) {
            // 初始化的时候不重复调用接口
            !isInit && this.reSetForm();
          } else {
            // 如果更改了测评工具，则需要重新去处理评估关系、评估权重
            // 这些字段都在 form.evaluationConfig4Create里
            // 之前无前测id时，逻辑是更改工具，重置评估关系和基本信息
            this.resetFormEvaluationConfig();
          }
        }

        // 问卷评估且仅支持他评，自评选项清空
        if (this.form.type === 2 && this.selfAssess === 0) {
          this.form.evaluationConfig4Create.enabledSelf = 0;
        }
      }

      // 评估关系，只能以dimensionInfoBeans中的数据为准
      if (this.selectedUtil && this.selectedUtil.dimensionInfoBeans && this.selectedUtil.dimensionInfoBeans.length) {
        this.setRelations(this.selectedUtil.dimensionInfoBeans);
      }
      // 非行为评估进行，行为评估在获取新的list后自己去处理checkedAllDimension
      // 因为行为评估的isSelect传入的也是false,在reset方法里
      if (isSelect) this.checkedAllDimension(isSelect);
      this.surveyChanged = false;
    },

    async getBehaviorList(id = '') {
      await this.getListAndSetRelation(id, 'defaultDimensionInfo');
    },

    // 设置表单基础信息
    setFormVal(result) {
      const {
        autoIssueReport,
        name,
        startTime,
        endTime,
        description,
        remark,
        charged,
        modelId,
        modelName,
        type,
        scene,
        headerList,
        isImport,
        indicators
      } = result;
      this.form = {
        autoIssueReport,
        name,
        time: startTime && endTime
          ? [
            dayjs(startTime).format('YYYY-MM-DD HH:mm:ss'),
            dayjs(endTime).format('YYYY-MM-DD HH:mm:ss')
          ]
          : null,
        description,
        remark,
        charged,
        modelId,
        modelName,
        type,
        scene,
        owners: headerList || [],
        isImport: isImport || 0
      };
      if ([1, 4].includes(type) && indicators && indicators.length > 0) { // 1-行为,4-多次行为评估
        this.form.indicatorIds = indicators.map(item => item.skillId);
      }
    },

    // 设置表单评估关系
    setFormEva(result) {
      const { evaluationConfig } = result;
      // 判断评估关系是否为空，为空则取data默认值
      if (evaluationConfig && Object.keys(evaluationConfig).length) {
        this.setCustomEvalConfig(evaluationConfig);
        this.$set(this.form, 'evaluationConfig4Create', evaluationConfig);
        // 设置作答设置
        this.setEvalConfigVal(evaluationConfig);
      } else {
        const orgionData = this.$options.data.call(this);
        this.$set(this.form, 'evaluationConfig4Create', orgionData.form.evaluationConfig4Create);
      }

      this.updateEvaluationConfig(this.form.evaluationConfig4Create);
    },

    async setEvalData(id) {
      this.loading = true;
      await getEvalData(id, this.skillModelId)
        .then(res => {
          this.loading = false;

          // 设置表单基本信息
          this.setFormVal(res);
          // 设置表单评估关系
          this.setFormEva(res);

          const { evaluationConfig, targetId } = res;

          // 赋值给变量，后面如果切换到第一步更换工具的时候可以有参数可以对比
          this.oldSurveyId = targetId;
          // 如果直接step传入2的时候，这个时候直接获取接口列表里的数据
          // true代表不需要自己去勾选对应的评估关系
          this.showInfo && this.getIdBehaviorList(true);
          // 更新问卷信息，方便上一步选择工具使用
          if (!this.surveyId) {
            this.$emit('update:surveyId', targetId);
          }
          // 如果是前测，则单独处理部分字段
          this.bfId && this.beforeIdHandle(evaluationConfig);
          this.$emit('update:isSelfTest', this.form);
          this.$emit('update:scene', this.form.scene);
          // 如果是后测, 则要删除多次评估选项
          if (res.afterEvalMark === 1) {
            this.$emit('update:isAfterTest', true);
          }

          // surveyId 等响应式传递，emit update后，必须等待【父】->【子】值成功传递后才能获取数据
          this.$nextTick(() => { // 编辑操作时 不需要全选
            this.getCurSurvey(false, true);
          });
        })
        .catch(err => {
          this.loading = false;
          this.handleError(err);
        });
    },

    beforeIdHandle(config) {
      this.$set(this.form, 'name', `${this.form.name}（${this.$t('pc_gwnl_training_postEval' /* 后测 */)}）`);

      if (config && Object.keys(config).length) {
        this.oldWeightType = config.weightType;
        this.oldEvaluationConfig = config;

        // 针对前测的配置进行处理
        let dimensionInfoBeans = (config.customEvalConfigs || []).map(item => {
          return { type: item.relationType };
        });
        this.defaultTypes.forEach((item, index) => {
          if (config[item]) {
            const infoIndex = index + 1;
            dimensionInfoBeans.push({ type: infoIndex.toString() });
          }
        });

        this.$set(this.oldSurveyUtil, 'dimensionInfoBeans', dimensionInfoBeans);
      }

      // 触发type修改，防止前测是行为，后测默认是工具，此时接口获取的type是行为，导致传入的保存的参数不对
      // 使用 nextTick，是保证上面oldSurveyUtil修改后，再触发type、changeAssess
      this.$nextTick(() => {
        this.$set(this.form, 'type', this.type);
        this.$emit('changeAssess', this.type);
      });
    },

    // 获取方案信息
    async getEvalInfo() {
      // 如果有前测的id, 则优先处理前测的调用关系
      if (this.bfId) {
        this.firstCreateEval = 0;
        this.setEvalData(this.beforeId);
        return;
      }

      if (!this.id) {
        // 行为评估才进行调用处理
        if (!this.isBehavior) return;
        await this.getBehaviorList();
        this.checkedAllDimension();

        if (this.owners.length) {
          this.form.owners = this.owners;
        }
        return;
      };

      await this.setEvalData(this.id);
      // 只有初始化进来的时候修改type类型
      this.$emit('changeAssess', this.form.type);
    },
    async reset() {
      this.resetData();
      // resetData & getCurSurvey都会触发规则，所有都要完成后清除下
      this.$nextTick(this.$refs.form.clearValidate);
      try {
        await this.getCurSurvey(!this.isBehavior);
        this.loading = false;
        this.$nextTick(this.$refs.form.clearValidate);
      } catch (err) {
        console.log(err);
      }

      // 前后测关联时跟随前测处理，不处理成默认值
      if (!this.bfId) {
        this.form.owners = this.owners;
      }
    },

    setEvaluationConfigSwitch(evaluationConfig4Create) {
      const isBeiEval = this.isBeiEval;

      return {
        ...(isBeiEval && this.$refs.setting.getRefBeiEvalConfigVal())
      };
    },

    // 调用保存接口方法
    postEvalInfo(callback, option) {
      let {
        name,
        type,
        description,
        remark,
        modelId,
        modelName,
        evaluationConfig4Create,
        autoIssueReport,
        owners,
        isImport
      } = this.form;
      name = name.trim();
      evaluationConfig4Create = { ...evaluationConfig4Create, ...this.weight };

      // 获取开关配置
      const configSetting = this.setEvaluationConfigSwitch(evaluationConfig4Create);
      evaluationConfig4Create = { ...evaluationConfig4Create, ...configSetting };

      evaluationConfig4Create.customEvalConfigs = [];

      // 专门针对上级评估处理
      if (!evaluationConfig4Create.enabledLeader) {
        evaluationConfig4Create.enabledLeaderDm = 0;
        evaluationConfig4Create.enabledLeaderPm = 0;
      }

      // 处理自定义评估关系
      this.evalRelation && this.evalRelation.forEach(item => {
        if (item.isDefault && (this.weight[item.weight] || this.weight[item.weight] === 0)) {
          evaluationConfig4Create.customEvalConfigs.push({
            relationType: item.type,
            dimensionName: item.label,
            weight: this.weight[item.weight] || 0
          });
        }
      });

      let headerList = owners.map(user => {
        return {
          id: user.id,
          fullname: user.fullname
        };
      });

      let data = {
        name,
        type: this.scene === 2 ? 3 : ([1, 4].includes(type) ? this.type : type), //  1-行为,2-问卷,3-人格,4-多次行为评估
        description,
        remark,
        startTime: this.form.time ? `${this.form.time[0]}` : '',
        endTime: this.form.time ? `${this.form.time[1]}` : '',
        scene: this.scene, // 1-胜任力 2-人格 3-通用
        sourceType: this.sourceType,
        modelId,
        modelName,
        evaluationConfig4Create,
        surveyId: this.surveyId,
        autoIssueReport,
        evalPurpose: this.purposeId,
        headerList: headerList,
        isImport
      };
      if (this.id) data.id = this.id;

      if ([1, 4].includes(type)) { // 1-行为,4-多次行为评估
        // 如果props里有indicatorIds，则说明是从上个页面过来的
        // 如果indicatorIds有值，则使用indicatorIds，否则使用form.indicatorIds
        if (this.indicatorIds.length > 0) {
          data.indicatorIds = this.indicatorIds;
        } else {
          data.indicatorIds = this.form.indicatorIds;
        }
      }
      // 新增、保存
      this.operation[`${OPERATION_MAP.get(option)}`] = true;
      this.isSubmitting = true;

      if (this.isBehavior) {
        delete data.autoIssueReport;
      }

      saveEval(data)
        .then(res => {
          this.operation[`${OPERATION_MAP.get(option)}`] = false;

          let id = '';
          if (res.Location && !this.id) {
            id = res.Location.split('saveEvaluation/')[1];
            // 首次创建需要测评绑定地图关系
            this.bindMap(id, this.type === 4 ? 1 : 0);
            // 保存即更新id给父组件
            this.$emit('update:id', id);
          }

          const afterIdFunc = () => {
            // 创建完-如果是行为评估，要清除工具类型的缓存
            // 创建完-如果是工具评估，则要重新赋值新的surveyId
            this.oldSurveyId = [1, 4].includes(data.type) ? '' : this.surveyId;
            this.isSubmitting = false;
            if (option === 1) {
              this.$message.success(
                this.$t('pc_gwnl_global_msg_savedSuccessfully')
              );
            }
            callback && callback();
          };

          // 确认后测的id
          const afterEvaluationId = (id || this.id);
          if (this.beforeId && afterEvaluationId) {
            // noNeedCopyAfterEvalUserFlag 是否需要复制后侧人员标识 0:默认复制, 1不需要复制
            // 只需要判断是否时第一次进入创建后测，是否需要复制人员即可
            postAfterEvaluation({
              preEvaluationId: this.beforeId,
              surveyId: this.surveyId || '',
              afterEvaluationId,
              modelId: this.skillModelId,
              noNeedCopyAfterEvalUserFlag: this.firstCreateEval
            }).then(() => {
              this.firstCreateEval = 1;
              afterIdFunc();
            }).catch(err => {
              this.handleError(err);
            });
            return;
          }

          afterIdFunc();
        })
        .catch((err) => {
          this.operation[`${OPERATION_MAP.get(option)}`] = false;
          this.isSubmitting = false;
          this.handleError(err);
        });
    },

    handleOpenOwner(val) {
      if (val) {
        this.selectUserVisible = true;
      }
    },
    getUsers(data) {
      this.form.owners = data;
      this.selectUserVisible = false;
    }
  }
};
</script>

<style lang='scss' scoped>
.gwnl-position-eval__info {
  ::v-deep .yxt-form-item__tooltip svg {
    width: 100% !important;
  }

  padding: 0 24px 40px 0;

  .yxt-radio {
    margin-right: 24px;
  }

  .pb2 {
    padding-bottom: 2px;
  }

  .weight-item {
    max-width: calc(100% - 24px);
    margin-right: 16px;
  }
}
</style>
