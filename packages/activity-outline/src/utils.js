import commonUtil from 'yxt-biz-pc/packages/common-util';
import { checkActivity, saveRecentLearn } from './service';

const rootNode = window;
const overflowScrollReg = /scroll|auto/i;
export function getScrollParent(element) {
  let node = element;
  while (
    node &&
    node.nodeType === document.ELEMENT_NODE &&
    node.tagName !== 'HTML' &&
    node.tagName !== 'BODY' &&
    node !== rootNode
  ) {
    let { overflowY } = window.getComputedStyle(node);

    if (overflowScrollReg.test(overflowY)) {
      return node;
    }

    node = node.parentNode;
  }

  return rootNode;
}

/**
 * 获取地址信息
 * @param {string} h5Url h5地址
 * @param {string} pcUrl pc地址
 * @param {number} isv 是否匿名 0 内部 1 游客
 */
export const generateShortUrl = async(h5Url, pcUrl, isv = 0) => {
  try {
    const shortUrl = await commonUtil.getShortUrl(pcUrl, h5Url, isv);
    return { url: shortUrl };
  } catch (error) {
    return { url: '' };
  }
};

export const getShortUrl = async function(pcUrl, h5Url, visitor) {
  pcUrl = pcUrl.trim().startsWith('/') ? pcUrl : '/' + pcUrl;
  h5Url = h5Url.trim().startsWith('/') ? h5Url : '/' + h5Url;
  const res = await generateShortUrl(h5Url, pcUrl, visitor);
  return res;
};

export const getLanguage = () => {
  return commonUtil.getLanguage() || 'zh';
};

export const goActivity = function(rootPath, activity, extData) {
  return new Promise((resolve, reject) => {
    const { id, actvId } = activity;
    const {registryId} = extData;
    checkActivity(rootPath, { id, actvId, regId: registryId })
      .then(res => {
        let { code, message } = res;
        if (code === 0) {
          // 后端记录学员最近学习的任务
          return saveRecentLearn(rootPath, { id, actvId, regId: registryId }).then(() => {
            // 任务详情
            const { projectId, registryId } = extData;
            const origin = window.location.origin;
            const url = `${origin}/ulcd/#/model/study?projectId=${projectId}&actvRegId=${registryId}&taskId=${id}`;
            openUrl(url);
            resolve();
          });
        } else {
          this.$message.error(message);
          reject();
        }
      })
      .catch(err => {
        console.log('er', err);
        reject(err);
      });
  });
};

export const openUrl = (url, newWindow) => {
  if (newWindow) {
    window.open(url);
  } else {
    window.location.href = url;
  }
};

/**
 * 获取操作和数据权限配置数据
 * @param {Object} designerConfig
 * @returns
 */
export const getAuthDataAndOperation = designerConfig => {
  if (!designerConfig || !designerConfig.manageConfigBean) {
    return {
      dataPermissionCodes: {},
      operationPermissionCodes: {},
      functionCode: ''
    };
  }
  let {manageConfigBean: {authDataPermissions, authPointPermissions, navPermission}} = designerConfig;
  authDataPermissions = authDataPermissions || [];
  authPointPermissions = authPointPermissions || [];

  const dataPermissionCodes = authDataPermissions.reduce((acc, item) => {
    acc[item.originalPermissionCode] = item.customPermissionCode;
    return acc;
  }, {});

  const operationPermissionCodes = authPointPermissions.reduce((acc, item) => {
    acc[item.originalPermissionCode] = item.customPermissionCode;
    return acc;
  }, {});
  return {
    dataPermissionCodes,
    operationPermissionCodes,
    functionCode: navPermission.customPermissionCode
  };
};
