<template>
  <!-- <div class="yxtbiz-activity-outline"> -->
  <component ref="component" :is="current" v-bind="$props" v-on="$listeners" />
  <!-- </div> -->
</template>

<script>
import ManageOutline from './components/manage-outline/index.vue';
import DetailOutline from './components/detail-outline/index.vue';
import LearnerOutline from './components/learner-outline/index.vue';
import baseMixin from './mixins/outline-mixin';
import navScrollMixin from './mixins/nav-scroll-mixin';

export default {
  name: 'YxtbizActivityOutline',
  name_zh: '活动大纲',
  components: {
    ManageOutline,
    DetailOutline,
    LearnerOutline
  },
  data() {
    return {};
  },
  eventList: [
    ...ManageOutline.eventList,
    ...DetailOutline.eventList,
    ...LearnerOutline.eventList,
    ...baseMixin.eventList
  ],
  props: {
    componentType: {
      required: true,
      type: String,
      default: 'manage',
      validator: value => ['manage', 'detail', 'learner'].includes(value),
      desc: '测训项目需要区分前后测的测评类型',
      setter: 'SelectSetter',
      enums: [
        {
          label: '管理大纲',
          value: 'manage'
        },
        {
          label: '学员详情',
          value: 'detail'
        },
        {
          label: '学员学习',
          value: 'learner'
        }
      ]
    },
    ...baseMixin.props,
    ...ManageOutline.props,
    ...DetailOutline.props,
    ...LearnerOutline.props,
    ...navScrollMixin.props
  },
  methods: {
    /**
     * 学员端对外暴露跳转活动方法
     */
    goActivity(activity) {
      typeof this.$refs.component.goActivity === 'function' &&
        this.$refs.component.goActivity(activity);
    },

    /**
     * 学员端对外暴露跳转活动方法
     */
    refreshList() {
      typeof this.$refs.component.refreshList === 'function' &&
        this.$refs.component.refreshList();
    }
  },
  computed: {
    current() {
      // 根据组件类型返回对应的组件
      const map = {
        manage: ManageOutline,
        detail: DetailOutline,
        learner: LearnerOutline
      };
      return map[this.componentType];
    }
  }
};
</script>
