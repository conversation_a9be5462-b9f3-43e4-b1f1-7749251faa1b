/**
 * 活动大纲 mixin
 * 2. 选修/必修文案处理
 * 3. 统一配置接口获取
 * mixin 先于组件执行
 */
import { getAllDesignerConfig } from '../service';

export default {
  props: {
    projectId: {
      required: true,
      type: String,
      desc: '项目id',
      setter: 'StringSetter'
    },
    registryId: {
      required: true,
      type: String,
      desc: '设计器注册id',
      setter: 'StringSetter'
    },
    rootPath: {
      type: String,
      required: true,
      desc: '接口根路径（无需路径分隔符）',
      setter: 'StringSetter'
    }
  },
  eventList: [
    // 组件里抛出的事件名，需要开发者自己梳理
    {
      type: 'init-start', // 具体的抛出的事件名
      handler: 'handleInitStart', // 事件处理函数
      label: '开始加载列表' // 事件描述
    },
    {
      type: 'init-end', // 具体的抛出的事件名
      handler: 'handleInitEnd', // 事件处理函数
      label: '列表加载结束，返回任务列表' // 事件描述
    }
  ],
  data() {
    return {
      compulsoryText: '',
      electiveText: '',
      designerConfig: {},
      initLoading: true
    };
  },
  provide() {
    const self = this;
    const customText = {};
    Object.defineProperty(customText, 'electiveText', {
      enumerable: true,
      get() {
        return self.electiveText;
      }
    });

    Object.defineProperty(customText, 'compulsoryText', {
      enumerable: true,
      get() {
        return self.compulsoryText;
      }
    });
    return {
      customText,
      designerConfig: this.designerConfig
    };
  },
  computed: {
    rootPathComputed() {
      // 替换掉 rootPath 两侧的 /
      return this.rootPath.replace(/^\/|\/$/g, '');
    }
  },
  async created() {
    await this.getAllDesignerConfig();
    this.fetchList();
  },
  methods: {
    initStart(type) {
      this.initLoading = true;
      this.$emit('init-start', {type: type || 'init'});
    },
    initEnd(data, type) {
      this.initLoading = false;
      this.$emit('init-end', {type: type || 'init', activityList: data});
    },
    getPeriodText() {
      const [
        { catalogName, catalogNameI18n }
      ] = this.designerConfig.functionConfigBean.catalogConfig.filter(
        item => item.catalogLevel === 1
      );
      return catalogNameI18n ? this.$t(catalogNameI18n) : catalogName;
    },

    async getAllDesignerConfig() {
      try {
        const data = await getAllDesignerConfig(this.rootPathComputed, {
          registryId: this.registryId
        });
        this.compulsoryText =
          data.functionConfigBean.requiredElectiveConfig.requiredAlias;
        this.electiveText =
          data.functionConfigBean.requiredElectiveConfig.electiveAlias;
        debugger;
        Object.assign(this.designerConfig, data);
      } catch (e) {
        this.compulsoryText = this.$t('pc_o2o_lbl_obligatory' /* 必修 */);
        this.electiveText = this.$t('pc_o2_o_lbl_elective' /* 选修 */);
      }
    },
    /**
     * 获取隐藏的阶段， 同时计算阶段下面的任务数量
     * @param {Array} list
     * @returns
     */
    getUnhiddenPeriods(list) {
      if (!list || !list.length) {
        return list;
      }
      list = list.filter(item => item.itemType !== 1 || !item.hidden);
      return list;
    },
    getRequiredActivities(list) {
      function filterChildren(children) {
        return children
          .map(child => {
            // 过滤子节点
            if (child.itemType === 0 && child.required === 1) {
              return { ...child };
            }
            const filteredChildren = filterChildren(child.children || []);
            // 如果是阶段或活动组且有子节点，保留
            if (
              (child.itemType === 1 || child.itemType === 2) &&
              filteredChildren.length > 0
            ) {
              return { ...child, children: filteredChildren };
            }
            return null; // 不保留不符合条件的节点
          })
          .filter(Boolean); // 过滤掉 null
      }
      if (!list || !list.length) {
        return [];
      }
      return filterChildren(list);
    },
    /**
     * 过滤出非空活动的阶段和任务组
     * @param {*} list
     * @returns
     */
    filterNonemptyActivity(list, hasPeriod = true) {
      // 递归过滤函数
      if (!list || !list.length) {
        return list;
      }
      if (hasPeriod) {
        return list.filter(period => {
          if (!period.children || period.children.length === 0) {
            return false;
          } // 空阶段
          period.children = period.children.filter(item => {
            if (
              item.itemType === 2 &&
              (!item.children || item.children.length === 0)
            ) {
              // 空组
              return false;
            }
            return true;
          });
          return true;
        });
      } else {
        return list.filter(item => {
          if (
            item.itemType === 2 &&
            (!item.children || item.children.length === 0)
          ) {
            // 空组
            return false;
          }
          return true;
        });
      }
    },
    getIconMap() {
      const datas = this.designerConfig.moduleList || [];
      return datas.reduce((acc, cur) => {
        acc[cur.registryId] = cur.iconUrl;
        return acc;
      }, {});
    },

    /**
     * 填充自定义字段
     * @param {*} list
     */
    supplementActivityField(list) {
      const recursionActivityField = (list, iconMap) => {
        if (!list || !list.length) {
          return list;
        }
        list.forEach(item => {
          if (item.itemType === 0) { // 活动
            item.iconUrl = iconMap[item.refRegId] || '';
            return;
          }
          if (item.itemType === 1) { // 阶段
            item.periodText = this.getPeriodText(); // 获取阶段文案
          }
          if (item.children && item.children.length) {
            recursionActivityField(item.children, iconMap);
          }
        });
      };
      const iconMap = this.getIconMap();
      recursionActivityField(list, iconMap);
    }
  }
};
