<template>
  <yxt-sticky bg="white" autoSize :offset="offset" animated>
    <div ref="navWrapper">
      <yxt-row type="flex" align="top" justify="space-between">
        <ul v-if="showPeriod" class="yxtbiz-period-nav">
          <li
            v-for="(period, index) in list"
            @click="handleClick(index)"
            :key="period.id"
            class="yxtbiz-period-nav__item"
            :class="{ 'yxtbiz-period-nav__item--active': period.id === value }"
          >
            <div class="text-center">
              <div class="yxtbiz-period-nav__title">
                {{ $t('pc_biz_period_no' /* 阶段{0} */, [index + 1]) }}
              </div>
              <yxt-progress
                v-if="!period.passRate || period.passRate < 100"  
                type="circle"
                :stroke-width="3"
                outColor="#D9D9D9"
                :percentage="period.passRate || 0"
                :show-text="false"
                :width="16"
              ></yxt-progress>
              <yxt-svg
                v-else
                class="color-green-6"
                :remote-url="`${$staticBaseUrl}ufd/55a3e0/o2o/pc/svg`"
                icon-class="pro-task3"
                width="18px"
                height="18px"
              ></yxt-svg>
            </div>
          </li>
        </ul>
        <yxt-switch
          class="yxtbiz-flex-shrink-0"
          v-model="onlyRequired"
          @change="$emit('switch-change', $event)"
          :active-text="$t('pc_biz_only_required' /* 只看必修 */)"
        />
      </yxt-row>
      <!-- 估计组件中动态计算高度的时候忽略了padding-bottom -->
      <div style="height: 24px"></div>
    </div>
  </yxt-sticky>
</template>

<script>
export default {
  props: {
    offset: {
      type: Number,
      default: 80
    },
    list: {
      type: Array,
      default: () => []
    },
    value: {
      type: String,
      default: 0
    },
    showPeriod: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      onlyRequired: false
    };
  },
  methods: {
    handleClick(index) {
      const period = this.list[index];
      this.$emit('input', period.id);
      if (this.value !== period.id) {
        this.$emit('change', period.id, index);
      }
    },
    setSelectedWithScroll(index) {
      this.handleClick(index);
    },
    getNavDomData() {
      return {
        clientHeight: this.$refs.navWrapper.clientHeight,
        paddingBottom: 24
      };
    }
  }
};
</script>

<style lang="scss" scoped>
$root: 'yxtbiz-period-nav';
.#{$root} {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-right: 16px;
  margin-top: -16px;

  &__item {
    flex-shrink: 0;
    min-width: 56px;
    height: 56px;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    margin-right: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    user-select: none;
    margin-top: 16px;

    &:hover {
      background: #f5f5f5;
      border-color: #f5f5f5;
    }
  }

  &__item--active {
    background: var(--color-primary-1) !important;
    border-color: var(--color-primary-1) !important;
  }

  &__title {
    font-size: 12px;
    color: #9a9a9a;
    line-height: 20px;
    margin-bottom: 2px;
  }
}
</style>
