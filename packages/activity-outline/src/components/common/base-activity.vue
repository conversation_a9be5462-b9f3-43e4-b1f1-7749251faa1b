<template>
  <div class="yxtbiz-base-activity">
    <yxt-row
      :class="{ 'pt0-impt': !hasPaddingTop }"
      class="pv16"
      type="flex"
      align="middle"
    >
      <yxt-svg
        class="yxtbiz-flex-shrink-0 mr8"
        :remote-url="icon.remoteUrl"
        width="40px"
        height="40px"
        :icon-class="icon.iconClass"
      />

      <div class="col-flex-1 over-hidden">
        <yxt-tooltip open-filter :content="activity.refName">
          <div class="yxtbiz-base-activity__title ellipsis">
            {{ activity.refName }}
          </div>
        </yxt-tooltip>
        <div class="yxtbiz-base-activity__tags">
          <yxt-tag size="mini" class="mt8 mr4">{{
            activity.actvAlias || $t(activity.typeNameKey)
          }}</yxt-tag>
          <yxt-tag
            v-if="activity.required === 0"
            type="info"
            size="mini"
            class="mr4 mt8"
            >{{ customText.electiveText }}</yxt-tag
          >
          <yxt-tag v-else type="warning" size="mini" class="mr4 mt8">{{
            customText.compulsoryText
          }}</yxt-tag>
        </div>
        <slot name="bottom"></slot>
      </div>
      <div v-if="$slots.default" class="ml32">
        <slot></slot>
      </div>
    </yxt-row>
    <yxt-divider class="yxtbiz-base-activity__line" v-if="line" />
  </div>
</template>

<script>
export default {
  inject: ['customText'],
  props: {
    activity: {
      required: true,
      default: () => ({})
    },
    line: {
      type: Boolean,
      default: true
    },
    hasPaddingTop: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    icon() {
      const { iconUrl } = this.activity;
      const lastSlashIndex = iconUrl.lastIndexOf('/');
      const lastDotIndex = iconUrl.lastIndexOf('.');
      return {
        remoteUrl: iconUrl.slice(0, lastSlashIndex),
        iconClass: iconUrl.slice(lastSlashIndex + 1, lastDotIndex)
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.yxtbiz-base-activity {
  &__title {
    font-size: 16px;
    color: #262626;
    line-height: 24px;
  }

  &__tags {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }
  &__line {
    margin: 0 !important;
  }
}
</style>
