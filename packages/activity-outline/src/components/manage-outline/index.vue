<template>
  <div
    :style="{ height: height }"
    v-loading="initLoading"
    ref="scrollContainer"
    class="yxtbiz-manage-outline"
  >
    <div class="yxtbiz-manage-outline__container">
      <div
        v-if="list.length > 0 && !taskUpdateTipHidden"
        class="yxtbiz-manage-outline__tip"
      >
        {{ $t('pc_biz_task_update_minute', [10]) }}
      </div>
      <div
        v-if="list.length === 0 && !initLoading"
        class="yxtbiz-manage-outline__empty"
      >
        <CustomEmpty>
          <yxt-button @click="$emit('arrange-activity')" type="primary">{{
            $t('pc_biz_go_to_add' /* 去添加 */)
          }}</yxt-button>
        </CustomEmpty>
      </div>
      <template v-if="hasPeriod">
        <!-- 有阶段 -->
        <BasePeriod
          v-for="(period, periodIndex) in list"
          :key="period.id"
          :period="period"
          :periodNo="periodIndex + 1"
          hiddenDescription
          :bottomLine="periodIndex !== list.length - 1"
        >
          <yxt-row
            type="flex"
            align="middle"
            style="line-height: normal"
            slot="suffix"
          >
            <yxt-tooltip
              effect="dark"
              :content="$t('pc_pd_lbl_period_locked')"
              placement="top"
              v-if="period.locked"
            >
              <yxt-svg
                class="ml12 color-gray-7 hand yxtbiz-flex-shrink-0"
                width="16px"
                height="16px"
                icon-class="icons/lock"
              />
            </yxt-tooltip>
            <yxt-tooltip
              v-if="period.hidden"
              effect="dark"
              :content="$t('pc_pd_lbl_period_hidden')"
              placement="top"
            >
              <yxt-svg
                class="ml12 color-gray-7 hand yxtbiz-flex-shrink-0"
                width="16px"
                height="16px"
                icon-class="common_hide"
              />
            </yxt-tooltip>
          </yxt-row>
          <div
            v-if="period.endDayOffset && period.endDayOffset > 0"
            class="yxtbiz-manage-outline__duration"
            slot="bottom"
          >
            {{
              $t('pc_biz_period_lifecycle' /* 阶段周期：{0}天 */, [
                period.endDayOffset
              ])
            }}
          </div>
          <template v-if="period.children && period.children.length > 0">
            <template v-for="(item, itemIndex) in period.children">
              <BaseGroup
                :showActivityTotal="false"
                :group="item"
                :key="item.id"
                v-if="item.itemType === 2"
                :bottomLine="itemIndex !== period.children.length - 1"
              >
                <template v-if="item.children && item.children.length > 0">
                  <ManageActivity
                    :activity="activity"
                    :key="activity.id"
                    v-for="(activity, activityIndex) in item.children"
                    :line="activityIndex !== item.children.length - 1"
                    :taskProgressHidden="taskProgressHidden"
                    :taskOperationHidden="taskOperationHidden"
                    @manage="handleManage(activity)"
                    @share="handleShare(activity)"
                  ></ManageActivity>
                </template>
                <CustomEmpty v-else />
              </BaseGroup>
              <ManageActivity
                v-else
                :activity="item"
                :key="item.id"
                :line="itemIndex !== period.children.length - 1"
                :taskProgressHidden="taskProgressHidden"
                :taskOperationHidden="taskOperationHidden"
                @manage="handleManage(item)"
                @share="handleShare(item)"
              ></ManageActivity>
            </template>
          </template>
          <CustomEmpty v-else />
        </BasePeriod>
      </template>
      <!-- 无阶段 -->
      <template v-else>
        <template v-for="(item, itemIndex) in list">
          <BaseGroup
            :showActivityTotal="false"
            :group="item"
            :key="item.id"
            v-if="item.itemType === 2"
            :bottomLine="itemIndex !== list.length - 1"
          >
            <template v-if="item.children && item.children.length > 0">
              <ManageActivity
                :activity="activity"
                :key="activity.id"
                v-for="(activity, activityIndex) in item.children"
                :line="activityIndex !== item.children.length - 1"
                @manage="handleManage(activity)"
                @share="handleShare(activity)"
              ></ManageActivity>
            </template>
            <CustomEmpty v-else />
          </BaseGroup>
          <ManageActivity
            :activity="item"
            :key="item.id"
            v-else
            :line="itemIndex !== list.length - 1"
            @manage="handleManage(item)"
            @share="handleShare(item)"
          ></ManageActivity>
        </template>
      </template>
    </div>

    <QrCode ref="qrCode" />
    <div
      v-if="authPoint.arrangeOutline && list.length > 0"
      class="yxtbiz-manage-outline__footer"
    >
      <yxt-button type="primary" @click="$emit('arrange-activity')">{{
        $t('pc_edit' /* 编辑 */)
      }}</yxt-button>
    </div>
  </div>
</template>

<script>
import CustomEmpty from '../common/empty.vue';
import BasePeriod from '../common/base-period.vue';
import BaseGroup from '../common/base-group.vue';
import ManageActivity from './manage-activity.vue';
import QrCode from './qr-code.vue';
import baseMixin from '../../mixins/outline-mixin';
import { getOutline4Manage } from '../../service';
import { getAuthDataAndOperation } from '../../utils';
import qs from 'qs';
import commonUtil from 'yxt-biz-pc/packages/common-util';
import { authPoint } from './resource-auth';

export default {
  name: 'YxtbizManageOutline',
  components: {
    BasePeriod,
    BaseGroup,
    CustomEmpty,
    ManageActivity,
    QrCode
  },
  eventList: [
    // 组件里抛出的事件名，需要开发者自己梳理
    {
      type: 'arrange-activity', // 具体的抛出的事件名
      handler: 'handleAddActivity', // 事件处理函数
      label: '添加活动' // 事件描述
    },
    {
      type: 'manage', // 具体的抛出的事件名
      handler: 'handleManage', // 事件处理函数
      label: '管理活动' // 事件描述
    }
  ],
  props: {
    projectStatus: {
      required: true,
      type: [Number, String],
      desc: '项目状态', // 0-未保存, 1-未发布, 2-进行中, 3-已结束, 4-归档中, 41-已归档, 42-归档超过一年, 5-已删除, 6-已暂停, 7-已撤回
      setter: 'StringSetter'
    },
    height: {
      required: false,
      type: [String],
      default: 'auto',
      desc: '高度, 需要带单位',
      setter: 'StringSetter'
    },
    autoScroll: {
      required: false,
      type: [Boolean],
      default: false,
      desc: '自动计算高度溢出滚动',
      setter: 'BooleanSetter'
    },
    taskUpdateTipHidden: {
      required: false,
      type: [Boolean],
      default: false,
      desc: '隐藏大纲顶部任务刷新提示',
      setter: 'BooleanSetter'
    },
    taskOperationHidden: {
      required: false,
      type: [Boolean],
      default: false,
      desc: '隐藏任务操作按钮',
      setter: 'BooleanSetter'
    },
    taskProgressHidden: {
      required: false,
      type: [Boolean],
      default: false,
      desc: '隐藏任务进度',
      setter: 'BooleanSetter'
    }
  },
  provide() {
    const projectStatus = {};
    const self = this;
    Object.defineProperty(projectStatus, 'value', {
      enumerable: true,
      get() {
        return self.projectStatus;
      }
    });
    return {
      projectStatus
    };
  },
  mixins: [baseMixin],
  data() {
    return {
      list: [],
      hasPeriod: 0
    };
  },
  mounted() {
    if (this.autoScroll) {
      // 外部设置高度后则不走内部高度计算逻辑
      this.setContainerHeight();
      window.addEventListener('resize', this.setContainerHeight);
      this.$on('hook:beforeDestroy', () =>
        window.removeEventListener('resize', this.setContainerHeight)
      );
    }
  },
  computed: {
    authPoint() {
      const { operationPermissionCodes, functionCode } =
        getAuthDataAndOperation(this.designerConfig);

      return {
        arrangeOutline: commonUtil.checkActionPermission(
          functionCode,
          operationPermissionCodes[authPoint.AOM_UACD_UPDATE]
        )
      };
    }
  },
  methods: {
    refreshList() {
      this.fetchList('refresh');
    },
    debounce(fn, delay) {
      let timer;
      return function(...args) {
        clearTimeout(timer);
        timer = setTimeout(() => fn.apply(this, args), delay);
      };
    },
    setContainerHeight() {
      // 外部也可以调用这个方法刷新高度
      const debouncedSetHeight = this.debounce(() => {
        const container = this.$refs.scrollContainer;
        if (container) {
          const height = this.getElementTopToViewportBottom(container);
          container.style.height = `${height}px`;
        }
      }, 100);
      debouncedSetHeight();
    },
    getElementTopToViewportBottom(container) {
      if (!container) return;
      const top = container.getBoundingClientRect().top;
      const viewportHeight = window.innerHeight;
      return viewportHeight - top;
    },
    handleManage(activity) {
      this.$emit('manage', activity);
    },
    handleShare(activity) {
      const { projectId, registryId } = this;
      const { id, refRegId, refId } = activity;
      const pcUrl = `/ulcd/#/model/study?${qs.stringify({
        projectId, // 项目id
        actvRegId: registryId, // 项目注册id
        taskId: id // 培训中任务id
      })}`;
      const h5Url = `auth/uacd/transfer?${qs.stringify({
        serverPath: this.rootPathComputed,
        projectId,
        taskId: id,
        actvRegId: registryId, // 项目设置器id
        taskActvRegId: refRegId, // 活动注册id/code
        targetId: refId
      })}`;
      this.$refs.qrCode.show({ pcUrl, h5Url, name: activity.refName });
    },
    async fetchList(type) {
      this.initStart(type);
      getOutline4Manage(
        this.rootPathComputed,
        {
          projectId: this.projectId
        },
        {
          regId: this.registryId
        }
      )
        .then(res => {
          const datas = res.datas || [];
          this.hasPeriod = res.hasFolder;
          if (this.hasPeriod) {
            this.supplementActivityField(datas);
            this.list = datas || [];
          } else {
            // 无阶段默认阶段处理
            const list = datas[0] ? datas[0].children || [] : [];
            this.supplementActivityField(list);
            this.list = list;
          }
        })
        .finally(() => this.initEnd(this.list, type));
    }
  }
};
</script>
<style lang="scss" scoped>
.yxtbiz-manage-outline {
  position: relative;
  overflow: hidden;

  &__container {
    height: 100%;
    min-height: 202px;
    overflow-y: auto;
    overflow-x: hidden;
    padding-bottom: 76px;
    box-sizing: border-box;
  }

  &__tip {
    font-size: 14px;
    padding: 8px 24px;
    color: #595959;
    line-height: 20px;
    background: #f5f5f5;
  }

  &__empty {
    padding-top: 220px;
  }

  &__duration {
    font-size: 14px;
    color: #595959;
    line-height: 22px;
  }

  &__footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    box-shadow: 0 -2px 12px 0 rgba(0, 0, 0, 0.08);
  }
}
</style>
