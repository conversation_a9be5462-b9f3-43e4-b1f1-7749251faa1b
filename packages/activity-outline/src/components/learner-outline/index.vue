<template>
  <div v-loading="initLoading" class="yxtbiz-learner-outline">
    <template v-if="listComputed.length > 0">
      <PeriodNav
        ref="PeriodNav"
        :showPeriod="showPeriodInNav"
        :offset="periodNavOffset"
        @switch-change="handleSwitchChange"
        :list="listComputed"
        v-model="activePeriodId"
        @change="handlePeriodNavChange"
      />
      <div>
        <!-- 有数据  -->
        <template v-if="listComputed.length > 0">
          <!-- 有阶段 -->
          <template v-if="project.hasFolder">
            <BasePeriod
              :hasPaddingTop="index !== 0"
              ref="basePeriodRef"
              :period="period"
              :periodNo="index + 1"
              v-for="(period, index) in listComputed"
              :key="period.id"
            >
              <div slot="suffix" class="yxtbiz-learner-outline__activity-total">
                {{
                  $t('pc_biz_activity_count' /* {0}个活动 */, [
                    period.leafCount
                  ])
                }}
              </div>
              <div slot="bottom" class="yxtbiz-learner-outline__progress">
                {{
                  period.locked
                    ? $t('pc_biz_o2o_lbl_unlocked' /* 未解锁 */)
                    : $t('pc_biz_progress_in_percent' /* 进度{0}% */, [
                        period.passRate
                      ])
                }}
              </div>
              <div
                v-if="period.overdueTime"
                slot="right"
                class="yxtbiz-learner-outline__overdue"
              >
                {{
                  $t('pc_biz_overdue_time' /* 逾期时间：{0} */, [
                    period.overdueTime
                  ])
                }}
              </div>
              <template v-if="period.children && period.children.length > 0">
                <template v-for="item in period.children">
                  <BaseGroup
                    :group="item"
                    :key="item.id"
                    v-if="item.itemType === 2"
                  >
                    <template v-if="item.children && item.children.length > 0">
                      <LearnerActivity
                        :periodLocked="period.locked"
                        @go-activity="goActivity"
                        :activity="activity"
                        v-for="activity in item.children"
                        :key="activity.id"
                      />
                    </template>
                    <CustomEmpty v-else />
                  </BaseGroup>
                  <LearnerActivity
                    :periodLocked="period.locked"
                    @go-activity="goActivity"
                    :activity="item"
                    :key="item.id"
                    v-else
                  />
                </template>
              </template>
              <CustomEmpty v-else />
            </BasePeriod>
          </template>
          <template v-else>
            <template v-for="item in listComputed">
              <BaseGroup
                :group="item"
                :key="item.id"
                v-if="item.itemType === 2"
              >
                <template v-if="item.children && item.children.length > 0">
                  <LearnerActivity
                    @go-activity="goActivity"
                    :activity="activity"
                    v-for="activity in item.children"
                    :key="activity.id"
                  />
                </template>
                <CustomEmpty v-else />
              </BaseGroup>
              <LearnerActivity
                @go-activity="goActivity"
                :activity="item"
                :key="item.id"
                v-else
              />
            </template>
          </template>
        </template>
      </div>
    </template>
    <!-- 无数据 -->
    <div v-else-if="listComputed.length === 0 && !initLoading">
      <CustomEmpty />
    </div>
  </div>
</template>

<script>
import BasePeriod from '../common/base-period.vue';
import BaseGroup from '../common/base-group.vue';
import PeriodNav from '../common/period-nav.vue';
import CustomEmpty from '../common/empty.vue';
import LearnerActivity from './learner-activity.vue';
import outlineMixin from '../../mixins/outline-mixin';
import navScrollMixin from '../../mixins/nav-scroll-mixin';
import { getOutline4Learner } from '../../service';
import { goActivity } from '../../utils';
import { debounce } from 'throttle-debounce';

export default {
  components: {
    PeriodNav,
    BasePeriod,
    BaseGroup,
    CustomEmpty,
    LearnerActivity
  },
  eventList: [],
  mixins: [outlineMixin, navScrollMixin],
  data() {
    this.scrollParent = null;
    return {
      activePeriodId: null,
      list: [],
      showRequiredList: false,
      requiredList: [],
      project: {}
    };
  },
  created() {
    this.debounceGoActivity = debounce(
      // 防止重复点击
      1000,
      true,
      activity => {
        const { projectId, registryId } = this;
        console.log('goActivity runs');
        goActivity(this.rootPathComputed, activity, { projectId, registryId });
      }
    );
  },
  computed: {
    listComputed() {
      return this.showRequiredList ? this.requiredList : this.list;
    },
    showPeriodInNav() {
      // 有阶段模式，同时阶段列表不为空
      return Boolean(this.project.hasFolder) && this.list.length > 0;
    }
  },
  methods: {
    /**
     * 获取最近学习活动
     * 如果没有则返回第一个活动
     */
    getRecentLearnActivity(list, result = {}) {
      if (!list || list.length === 0) return result;
      for (const el of list) {
        if (el.itemType === 0) {
          !result.firstActivity && (result.firstActivity = el);
          if (el.recentLearn) {
            result.recentLearn = el;
            return result;
          }
        } else if (el.children && el.children.length > 0) {
          const result = this.getRecentLearnActivity(el.children, result);
          if (result.recentLearn) return result;
        }
      }
      return result;
    },
    goActivity(activity) {
      if (!activity) throw new Error('activity param is required');
      this.debounceGoActivity(activity);
    },
    fetchList() {
      // mixin 中调用
      this.initStart();
      getOutline4Learner(this.rootPathComputed, {
        projectId: this.projectId,
        learnerId: this.learnerId
      }, {
        regId: this.registryId
      })
        .then(res => {
          let { datas, ...project } = res;
          datas = datas || [];
          this.project = project;
          if (this.project.hasFolder) {
            datas = this.filterNonemptyActivity(datas);
            datas = this.getUnhiddenPeriods(datas);
            this.supplementActivityField(datas);
            this.list = datas;
            this.$nextTick(() => {
              if (this.showPeriodInNav) {
                this.addScrollContainerHandler();
                this.setPeriodNavSelected(0, false);
              }
            });
          } else {
            // 无阶段默认阶段处理
            datas = datas[0] ? datas[0].children || [] : [];
            datas = this.filterNonemptyActivity(datas, false);
            this.supplementActivityField(datas);
            this.list = datas;
          }

          if (this.list.length > 0) this.emitRecentLearnActivity();
        })
        .finally(() => this.initEnd(this.list));
    },
    emitRecentLearnActivity() {
      const { recentLearn, firstActivity } = this.getRecentLearnActivity(
        this.list
      );
      this.$emit('recent-learn', recentLearn || firstActivity);
    },
    handleSwitchChange(value) {
      this.showRequiredList = value;
      if (this.list.length > 0) {
        this.requiredList =
          this.requiredList.length > 0
            ? this.requiredList
            : this.getRequiredActivities(this.list);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.yxtbiz-learner-outline {
  min-height: 202px;

  &__activity-total {
    font-size: 12px;
    color: #8c8c8c;
    line-height: 20px;
  }
  &__overdue {
    font-size: 14px;
    color: #757575;
    line-height: 22px;
  }
  &__progress {
    font-size: 14px;
    color: #595959;
    line-height: 22px;
  }
}
</style>
