<template>
  <BaseActivity class="yxtbiz-learner-activity" :activity="activity">
    <div
      v-if="activity.startTime"
      class="yxtbiz-learner-activity__time"
      slot="bottom"
    >
      {{
        $t('pc_biz_time_range' /* 起止时间：{start} ~ {end} */, {
          start: activity.startTime,
          end: activity.endTime
        })
      }}
    </div>
    <div class="pr16">
      <yxt-row
        type="flex"
        align="middle"
        class="yxtbiz-learner-activity__hover-show"
      >
        <!-- subType	子类型(1-Assessment, 2-Content, 3-Practice) -->
        <yxt-row
          v-if="
            activity.subType === 1 &&
              activity.assessmentResult.targetStatus === 4
          "
          class="yxtbiz-learner-activity__assessment mr12"
          type="flex"
          align="middle"
        >
          <div class="mr4">
            {{
              $t('pc_biz_ote_lbl_score_num' /* {0}分 */, [
                activity.assessmentResult.score
              ])
            }}
          </div>
          <div>
            {{
              $t(
                activity.assessmentResult.passed
                  ? 'pc_ote_lbl_pass' /* 通过 */
                  : 'pc_ote_lbl_notpass' /* 未通过 */
              )
            }}
          </div>
        </yxt-row>
        <yxt-button
          v-if="!periodLocked"
          @click="$emit('go-activity', activity)"
          type="primary"
          size="small"
          >{{ participateActivityText }}</yxt-button
        >
      </yxt-row>
      <yxt-row
        type="flex"
        align="middle"
        class="yxtbiz-learner-activity__hover-hidden"
      >
        <div
          v-if="activity.recentLearn"
          class="mr6 color-primary-6 lh20 font-size-12"
        >
          {{ $t('pc_biz_recent_learned' /* 最近学到 */) }}
        </div>
        <div v-if="activity.overdue" class="yxtbiz-learner-activity__status">
          {{ $t('pc_biz_overdue' /* 已逾期 */) }}
        </div>

        <yxt-svg
          v-if="periodLocked"
          class="yxtbiz-flex-shrink-0 ml12"
          :remote-url="`${$staticBaseUrl}ufd/55a3e0/o2o/pc/svg`"
          width="18px"
          height="18px"
          icon-class="pro-task0"
        />
        <yxt-svg
          v-else
          class="yxtbiz-flex-shrink-0 ml12 "
          :class="{ 'color-green-6': activity.resultStatus === 2 }"
          :remote-url="`${$staticBaseUrl}ufd/55a3e0/o2o/pc/svg`"
          width="18px"
          height="18px"
          :icon-class="`pro-task${mapStatusToIcon()}`"
        />
      </yxt-row>
    </div>
  </BaseActivity>
</template>

<script>
import BaseActivity from '../common/base-activity.vue';
import activityMixin from '../../mixins/activity-mixin';
export default {
  props: {
    periodLocked: Number
  },
  components: {
    BaseActivity
  },
  mixins: [activityMixin],
  computed: {
    participateActivityText() {
      const { resultStatus } = this.activity;
      switch (resultStatus) {
        case 0:
          return '开始';
        case 1:
          return '继续参加';
        case 2:
          return '查看';
        default:
          return '开始';
      }
    }
  }
};
</script>

<style lang="scss" scoped>
$root: '.yxtbiz-learner-activity';
#{$root} {
  &:hover {
    background: #fafafa;
    box-shadow: inset 0px -1px 0px 0px #e9e9e9;

    #{$root}__hover-hidden {
      display: none;
    }
    #{$root}__hover-show {
      display: flex;
    }
  }

  #{$root}__hover-show {
    display: none;
  }

  &__time {
    margin-top: 8px;
    font-size: 14px;
    color: #8c8c8c;
    line-height: 22px;
  }

  &__status {
    font-size: 12px;
    color: #8c8c8c;
    line-height: 20px;
  }

  &__result {
    font-size: 12px;
    color: #8c8c8c;
    line-height: 20px;
  }
  &__assessment {
    font-size: 14px;
    color: #595959;
    line-height: 22px;
  }
}
</style>
