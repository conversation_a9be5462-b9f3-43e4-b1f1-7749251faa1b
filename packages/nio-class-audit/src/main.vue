<template>
  <div class="yxtbiz-nio-class-audit">
    <div class="standard-size-16 color-gray-10">
      <div class="mb32 font-bolder">
        {{detail.operateType === 0 ? $t('pc_biz_nio_audit_project'/* 调班项目 */) : $t('pc_biz_nio_cancel_project' /*取消项目*/)}}
        <yxtf-row type="flex" class="mv24 font-normal font-size-14">
          <yxtf-col :span="24" class="nowrap mh2 felx-center">
            <span class="color-gray-7">{{$t('yxtbiz_ai_lbl_o2o'/* 培训项目 */)}}{{$t('pc_biz_enroll_lbl_colon' /* ： */)}}</span>
            <yxtf-tooltip :open-filter="true"
                          :content="detail.targetProjectName"
                          placement="top">
              <div class="yxtbiz-o2o-enroll-audit_ellipsis">{{ detail.targetProjectName || '--' }}</div>
            </yxtf-tooltip>
          </yxtf-col>
        </yxtf-row>
        <yxtf-row v-if="detail.operateType === 0" type="flex" class="mv24 font-normal font-size-14">
          <yxtf-col :span="24" class="nowrap mh2 felx-center">
            <span class="color-gray-7">{{$t('pc_biz_nio_origin_project'/* 原项目 */)}}{{$t('pc_biz_enroll_lbl_colon' /* ： */)}}</span>
            <yxtf-tooltip :open-filter="true" placement="top" :content="detail.projectName">
              <div class="yxtbiz-o2o-enroll-audit_ellipsis">{{detail.projectName || '--'}}</div>
            </yxtf-tooltip>
          </yxtf-col>
        </yxtf-row>
        <yxtf-row type="flex" class="mv24 font-normal font-size-14">
          <yxtf-col :span="24" class="nowrap mh2 felx-center">
            <span class="color-gray-7">{{ detail.operateType === 0 ? $t('pc_biz_nio_audit_desc'/* 调班说明 */) : $t('pc_biz_nio_cancel_reason' /*取消原因*/)}}{{$t('pc_biz_enroll_lbl_colon' /* ： */)}}</span>
            <yxtf-tooltip :open-filter="true" placement="top" :content="detail.auditDescribe">
              <div class="yxtbiz-o2o-enroll-audit_ellipsis">{{detail.auditDescribe || '--'}}</div>
            </yxtf-tooltip>
          </yxtf-col>
        </yxtf-row>
      </div>
    </div>

    <div class="standard-size-16 color-gray-10">
      <div class="mb32 font-bolder">
        {{$t('pc_biz_ac_enroller_info'/* 申请人信息 */)}}
        <yxtf-row type="flex" class="mv24 font-normal font-size-14">
          <yxtf-col :span="12" class="nowrap mh2 felx-center">
            <span class="color-gray-7">{{$t('pc_biz_rank_name'/* 姓名 */)}}{{$t('pc_biz_enroll_lbl_colon' /* ： */)}}</span>
            <yxtf-tooltip :open-filter="true"
                          :content="name"
                          placement="top">
              <div class="yxtbiz-o2o-enroll-audit_ellipsis">{{ name || '--' }}</div>
            </yxtf-tooltip>
          </yxtf-col>
          <yxtf-col :span="12" class="nowrap mh2 felx-center">
            <span class="color-gray-7">{{$t('pc_biz_rank_dept'/* 部门 */)}}{{$t('pc_biz_enroll_lbl_colon' /* ： */)}}</span>
            <yxtf-tooltip :open-filter="true"
                          :content="detail.applicantObj.deptName"
                          placement="top">
              <div class="yxtbiz-o2o-enroll-audit_ellipsis">{{ detail.applicantObj.deptName || '--' }}</div>
            </yxtf-tooltip>
          </yxtf-col>
        </yxtf-row>
        <yxtf-row type="flex" class="mv24 font-normal font-size-14">
          <yxtf-col :span="24" class="nowrap mh2 felx-center">
            <span class="color-gray-7">{{$t('pc_biz_bbs_lbl_possion'/* 岗位 */)}}{{$t('pc_biz_enroll_lbl_colon' /* ： */)}}</span>
            <yxtf-tooltip :open-filter="true" placement="top" :content="detail.applicantObj.positionName">
              <div class="yxtbiz-o2o-enroll-audit_ellipsis">{{detail.applicantObj.positionName || '--'}}</div>
            </yxtf-tooltip>
          </yxtf-col>
        </yxtf-row>
      </div>
    </div>
  </div>
</template>

<script>
import { FetchAuditInfo } from './service';

export default {
  name: 'YxtbizNioClassAudit',

  props: {
    formId: { type: String }
  },

  data() {
    return {
      detail: {
        applicantObj: {}
      }
    };
  },

  computed: {
    name() {
      return this.detail.applicantObj.fullname ? `${this.detail.applicantObj.fullname}（${this.detail.applicantObj.username}）` : '--';
    }
  },

  created() {
    FetchAuditInfo(this.formId).then(res => {
      this.detail = res.data;
    });
  }
};
</script>
