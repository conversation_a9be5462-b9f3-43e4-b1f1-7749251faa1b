<template>
  <div class="yxtbiz-eval-detail">
    <div class="quesBehave_header">
      <div class="shrink0 flex-g-1 mb16">
        <!-- 增加评估人 -->
        <div
          v-if="SOURCE_CODE.agentOperation !== source_code && isDoing && showAddEvalUser && type !== 4"
          class="inline-block"
        >
          <selectTarget
            v-if="isSelfeval"
            :datas="tableData"
            :relation-prev="relationPrev"
            :new-charge-survey-id="chargeSurveyId"
            :total="pager.total"
            :select-users="selectUsers"
            @addSuccess="changeDatas"
          />
          <track-person
            v-else
            :datas="tableData"
            :charge-survey-id="chargeSurveyId"
            @addSuccess="addSuccess"
          />
        </div>
        <!-- 设置评估关系 -->
        <div v-if="pandianSource && !isSelfAssess && detail.evaluationStatus < 2" class="d-in-block mr10">
          <yxt-button
            type="primary"
            @click.native="setEvalRelation"
          >
            {{ $t('pc_gwnl_eval_set_evalRelation') }}
          </yxt-button>
        </div>
        <!-- 分配测评份额 -->
        <div v-if="pandianSource && chargeSurveyId && (notStartEvalStatus || processEvalStatus)" class="d-in-block mr10">
          <yxt-button
            type="primary"
            @click.native="setEvalShare"
          >
            {{ $t('pc_biz_eval_lbl_allocate_evaluation_quota') }}
          </yxt-button>
        </div>
        <yxt-dropdown
          v-if="processEvalStatus && detail.finishPercent < 1"
          class="mr10"
          custom-elm
          :disabled="urgeBtnDisable || notStarte"
          @command="hurryChange"
          @visible-change="visible => urgeVisible = visible"
        >
          <yxt-button type="primary">
            {{ $t('pc_gwnl_eval_btn_urgeEval') }}<i :class="urgeVisible ? upIconClass : downIconClass" class="color-whilte-impt" />
          </yxt-button>
          <yxt-dropdown-menu slot="dropdown">
            <yxt-dropdown-item command="1">
              {{ $t('pc_gwnl_eval_btn_urgeEval') }}
            </yxt-dropdown-item>
            <yxt-dropdown-item command="2">
              {{ $t('pc_eval_urge_all_people') /** 催促全部未完成人员 */ }}
            </yxt-dropdown-item>
          </yxt-dropdown-menu>
        </yxt-dropdown>
        <!-- 第三方评估导入 -->
        <yxt-button
          v-if="isDoing && type === 1 "
          plain
          class="mr10"
          @click="importEval"
        >
          {{ $t('pc_gwnl_eval_introductThirdAssessment') }}
        </yxt-button>
        <!-- 批量操作 -->
        <yxt-dropdown
          v-if="type !== 4 && !notPublishEvalStatus && (batchButton && batchButton.length !== 0)"
          class="eval-detail__export-dropdown mr10"
          custom-elm
          @visible-change="visible => bathVisible = visible"
        >
          <yxt-button plain>
            {{ $t('pc_gwnl_global_btn_bathOperation') }}
            <i :class="bathVisible ? upIconClass : downIconClass" />
          </yxt-button>
          <yxt-dropdown-menu slot="dropdown">
            <!-- 批量删除 -->
            <yxt-dropdown-item
              v-for="(item, index) in batchButton"
              :key="index"
              :disabled="item.disabled"
              @click.native="item.method"
            >
              {{ $t(item.name) }}
            </yxt-dropdown-item>
          </yxt-dropdown-menu>
        </yxt-dropdown>
        <!-- 导出数据 -->
        <yxt-dropdown
          v-if="!notPublishEvalStatus"
          class="eval-detail__export-dropdown mr10"
          custom-elm
          @visible-change="visible => exportVisible = visible"
        >
          <yxt-button plain>
            {{ $t('pc_gwnl_global_exportData') }}
            <i :class="exportVisible ? upIconClass : downIconClass" />
          </yxt-button>
          <yxt-dropdown-menu slot="dropdown">
            <yxt-dropdown-item @click.native="exportFnc('person')">
              {{ $t('pc_gwnl_eval_export_finishedDetail') }}
            </yxt-dropdown-item>
            <yxt-dropdown-item @click.native="exportFnc('summary')">
              <!-- 导出能力标准分 -->
              {{ $t('pc_gwnl_eval_export_standard_countScore').d('导出标准分汇总') }}
            </yxt-dropdown-item>
            <yxt-dropdown-item @click.native="exportFnc('skillDetail')">
              {{ $t('pc_gwnl_eval_export_standard_detailScore').d('导出标准分明细') }}
            </yxt-dropdown-item>
            <yxt-dropdown-item
              v-if="evalInfo.type !== 1 && evalInfo.type !== 4 && !isBei"
              @click.native="exportFnc('original')"
            >
              {{ $t('pc_gwnl_eval_export_original_detailScore') }}
            </yxt-dropdown-item>
            <yxt-dropdown-item
              v-if="!!+detail.peerAssess && !isBei"
              @click.native="exportFnc('detailInfo')"
            >
              {{ $t('pc_eval_export_detailed_completion') }}
            </yxt-dropdown-item>
            <yxt-dropdown-item
              v-if="isCollectionItemSet === 1 && !isBei"
              @click.native="exportUserMsg()"
            >
              <!-- 导出收集的员工信息 -->
              {{ $t('pc_eval_export_collected_usermsg') }}
            </yxt-dropdown-item>
            <template v-if="isBei">
              <yxt-dropdown-item @click.native="exportFnc('reason')">{{ $t('pc_eval_btn_export_ai_rating_details' /* 导出AI评级明细 */) }}</yxt-dropdown-item>
              <yxt-dropdown-item v-if="!isDingDing" @click.native="exportFnc('record')">{{ $t('pc_eval_btn_export_answer_record' /* 导出作答记录 */) }}</yxt-dropdown-item>
            </template>
          </yxt-dropdown-menu>
        </yxt-dropdown>
      </div>
      <div class="table_wrapper_search mb16">
        <yxt-select
          v-if="!isRecruit && pandianSource && chargeSurveyId"
          v-model="params.quotaEnabled"
          class="mr12"
          :class="$isChinese ? 'default-selector' : 'w160'"
          size="small"
          collapse-tags
          :placeholder="$t('pc_eval_quota_status')"
          clearable
          @change="getList(1)"
        >
          <yxt-option
            v-for="item in occShareList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </yxt-select>
        <template v-if="!isRecruit">
          <yxt-select
            v-model="params.udpStatus"
            class="mr12"
            :class="$isChinese ? 'default-selector' : 'w160'"
            size="small"
            collapse-tags
            :placeholder="$t('pc_gwnl_userStatus')"
            clearable
            @change="getList(1)"
          >
            <yxt-option
              v-for="item in statusList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </yxt-select>
          <eval-select-department-tree
            multiple
            :children-included="false"
            :function-code="functionCode"
            :data-permission-code="deptDataPermissionCode"
            class="mr12"
            @change="selectDept"
          />
        </template>
        <div>
          <yxt-input
            v-model="params.keyword"
            searchable
            size="small"
            :class="$isChinese ? 'w216' : 'w240'"
            :placeholder="isRecruit ? $t('pc_eval_name_phone_search') : $t('pc_gwnl_eval_enterNamePosSearch')"
            @search="search"
          />
        </div>
      </div>
    </div>
    <yxt-table
      ref="multipleTable"
      v-loading="loading"
      :data="tableData"
      tooltip-effect="dark"
      class="width-percent-100"
      :default-sort="{ prop: 'evaluationFinishRate', order: 'desc' }"
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
    >
      <yxt-table-column
        type="selection"
        width="40"
        :selectable="handleMubeSelect"
      />
      <yxt-table-column
        show-overflow-tooltip
        :popover-html="true"
        prop="fullName"
        min-width="180"
        :label="$t('pc_gwnl_eval_lbl_assessee')"
      >
        <template slot-scope="scope">
          <yxtbiz-user-name :name="scope.row.fullName || '--'" />
          <span v-if="scope.row.userName">({{ scope.row.userName }})</span>
        </template>
      </yxt-table-column>
      <template v-if="!isRecruit && pandianSource">
        <!-- 评估关系状态 -->
        <yxt-table-column
          prop="setRelation"
          :label="$t('pc_biz_eval_lbl_evaluation_relationship_status' /* 评估关系状态 */)"
          :width="$isChinese ? '120' : '140'"
          hide-column="disable"
          show-overflow-tooltip
        >
        <template slot-scope="{ row }">
          <yxt-tag
            is-status-tag
            class="p0"
            :type="row.setRelation ? 'success' : 'warning'"
            size="mini">
            {{ $t(row.setRelation ? 'pc_ote_lbl_hasbeenset' /* 已设置 */ : 'pc_ote_lbl_notset' /* 未设置 */) }}
          </yxt-tag>
        </template>
        </yxt-table-column>
        <!-- 份额状态 -->
        <yxt-table-column
          v-if="chargeSurveyId"
          prop="quotaEnabled"
          :label="$t('pc_eval_quota_status' /* 份额状态 */)"
          :width="$isChinese ? '120' : '140'"
          hide-column="disable"
          show-overflow-tooltip
        >
          <template slot-scope="{ row }">
            <yxt-tag
              is-status-tag
              class="p0"
              :type="row.quotaEnabled ? 'success' : 'warning'"
              size="mini">
              {{ $t(row.quotaEnabled ? 'pc_eval_quota_occupied' /* 已占用 */ : 'pc_eval_quota_unoccupied' /* 未占用 */) }}
            </yxt-tag>
          </template>
        </yxt-table-column>
      </template>
      <!-- 游客展示字段 -->
      <template v-if="isRecruit">
        <yxt-table-column
          prop="mobile"
          :label="$t('pc_eval_phone' /**'手机' */)"
          :show-overflow-tooltip="false"
        >
          <template slot-scope="scope">
            {{ scope.row.mobile || '--' }}
          </template>
        </yxt-table-column>
        <yxt-table-column
          prop="email"
          :label="$t('pc_gwnl_global_lbl_email' /**'邮箱' */)"
        >
          <template slot-scope="scope">
            {{ scope.row.email || '--' }}
          </template>
        </yxt-table-column>
      </template>
      <!-- 游客不展示字段 -->
      <template v-else>
        <yxt-table-column
          v-if="type === 1 || type === 4"
          show-overflow-tooltip
          prop="modelName"
          min-width="140"
          :label="$t('pc_eval_model_name' /**'模型名称' */)"
        >
          <template slot-scope="scope">
            {{ scope.row.modelName || "--" }}
          </template>
        </yxt-table-column>
        <yxt-table-column
          v-if="!isRecruit"
          prop="userStatus"
          :label="$t('pc_gwnl_userStatus').d('账号状态')"
          :width="$isChinese ? '120' : '140'"
          hide-column="disable"
          show-overflow-tooltip
        >
          <template slot-scope="{row}">
            <yxt-tag
              v-if="[0,1,2].includes(row.userStatus)"
              class="p0"
              is-status-tag
              :type="statusMap.get(row.userStatus).type"
              size="mini"
            >
              {{ statusMap.get(row.userStatus).label }}
            </yxt-tag>
          </template>
        </yxt-table-column>
        <yxt-table-column
          v-if="!isRecruit"
          prop="deptName"
          :label="$t('pc_gwnl_global_lbl_dept')"
          :show-overflow-tooltip="false"
          min-width="180"
        >
          <template slot-scope="scope">
            <dept-cell :dept-name="scope.row.deptName" cell-position="static" />
          </template>
        </yxt-table-column>
        <yxt-table-column
          v-if="!isRecruit"
          show-overflow-tooltip
          prop="positionName"
          min-width="160"
          :label="$t('pc_gwnl_global_lbl_position')"
        >
          <template slot-scope="scope">
            {{ scope.row.positionName || "--" }}
          </template>
        </yxt-table-column>
        <yxt-table-column
          v-if="!isRecruit"
          show-overflow-tooltip
          prop="gradeName"
          min-width="130"
          :label="$t('pc_gwnl_global_lbl_grade')"
        >
          <template slot-scope="scope">
            {{ scope.row.gradeName || "--" }}
          </template>
        </yxt-table-column>
      </template>
      <yxt-table-column
        show-overflow-tooltip
        sortable="custom"
        prop="evaluationFinishRate"
        :label="$t('pc_gwnl_eval_lbl_evalFinishRate')"
        :width="$isChinese ? '135' : '180'"
      >
        <template slot-scope="scope">
          {{ getFinishScore(scope.row.state,scope.row.evaluationFinishRate) }}
        </template>
      </yxt-table-column>
      <yxt-table-column
        v-if="showExceptionAnswerColumn && !notPublishEvalStatus"
        prop="hasExceptionAnswer"
        :label="$t('pc_eval_answer_check' /* 作答检测 */)"
        :width="$isChinese ? '110' : '166'"
        show-overflow-tooltip
      >
        <template slot="headerIcon">
          <span class="yxt-table-header-options__icon color-gray-7">
            <yxt-tooltip placement="top" max-height="180">
              <div slot="content" v-html="dealLineFeed(exceptionAnswer)"></div>
              <yxt-svg width="16px" height="16px" icon-class="question-cirlce-o"></yxt-svg>
            </yxt-tooltip>
          </span>
        </template>
        <template slot-scope="{ row }">
          {{ row.evaluationFinishRate === 0 ? '--' : $t(row.hasExceptionAnswer === 0 ? 'pc_eval_status_normal' : 'pc_eval_status_abnormal') }}
        </template>
      </yxt-table-column>
      <yxt-table-column
        v-if="hasQuestionnaire && detail.personalReport && isBei && ![1, 4].includes(type)"
        :width="$isChinese ? '120' : '166'"
        :label="$t('pc_gwnl_eval_reportStatus')"
      >
        <template slot-scope="{ row }">
          <dept8-dot
            :custom-color="statusOptions[row.reportStatus] ? statusOptions[row.reportStatus].customColor : ''"
            :type="statusOptions[row.reportStatus] ? statusOptions[row.reportStatus].type : 0"
          >
            {{ statusOptions[row.reportStatus] ? statusOptions[row.reportStatus].label : '' }}
          </dept8-dot>
        </template>
      </yxt-table-column>
      <yxt-table-column
        v-if="multiBehavior"
        show-overflow-tooltip
        prop="evaluationFinishRate"
        :label="$t('pc_eval_lbl_sendState').d('下发状态')"
        width="135"
      >
        <template slot-scope="{row}">
          <yxt-tag
            v-if="[0,1,2].includes(row.state)"
            style="padding: 0;"
            is-status-tag
            :type="stateMap.filter(item=>item.value===row.state)[0].type"
            size="mini"
          >
            {{ stateMap.filter(item=>item.value===row.state)[0].label }}
          </yxt-tag>
        </template>
      </yxt-table-column>
      <yxt-table-column
        fixed="right"
        :label="$t('pc_gwnl_global_lbl_operate').d('操作')"
        :width="hasQuestionnaire && detail.personalReport && !isDingDing && isPdf ? beiOperateWidth : ($isChinese ? '96' : '116')"
      >
        <template slot-scope="scope">
          <!-- 查看-问卷及单次行为评估 -->
          <template v-if="multiBehavior===0">
            <span
              v-if="detail.evaluationStatus > -1 && (scope.row.evaluationFinishRate > 0 || scene !== 2)"
              class="pointer"
              @click="view(scope.row)"
            >{{ $t('pc_gwnl_global_btn_view').d('查看') }}</span>
            <span
              v-else
              class="color-gray-6"
            >{{ $t('pc_gwnl_global_btn_view').d('查看') }}</span>
          </template>
          <!-- 查看-多次行为评估 -->
          <template v-else>
            <span
              v-if="detail.evaluationStatus > -1 && scope.row.state === 1 && (scope.row.evaluationFinishRate > 0 || scene !== 2)"
              class="pointer"
              @click="view(scope.row)"
            >{{ $t('pc_gwnl_global_btn_view') }}</span>
            <yxt-tooltip
              v-else
              :content="$t('pc_eval_tip_mube_send').d('该人员尚未触发评估下发规则')"
              placement="top"
              :disabled="scope.row.state?true:false"
            >
              <span
                class="color-gray-6 cursor-disabled"
              >{{ $t('pc_gwnl_global_btn_view') }}</span>
            </yxt-tooltip>
          </template>
          <template v-if="hasQuestionnaire && detail.personalReport && !isDingDing && isPdf">
            <template v-if="isBei">
              <yxt-divider
                direction="vertical"
              />
              <yxt-button
                type="text"
                :disabled="downloadBtnDisabledComputed(scope.row) || clickBtnDownload(scope.row)"
                @click="downloadQuestionnaireReport(scope.row)"
              >
                {{ $t('pc_gwnl_eval_downloadReport' /* 下载报告 */) }}
              </yxt-button>
            </template>
            <span v-if="detail.personalReport">
              <yxt-divider direction="vertical" />
              <yxt-button
                type="text"
                :disabled="downloadBtnDisabledComputed(scope.row) || clickBtnIssue(scope.row)"
                @click="sendQuestionnaireReport(scope.row)"
              >
                {{ $t('pc_gwnl_eval_sendReport' /* 下发报告 */) }}
              </yxt-button>
            </span>
          </template>
        </template>
      </yxt-table-column>
    </yxt-table>
    <!--分页信息-->
    <div class="clearfix">
      <eval-pager
        :total.sync="pager.total"
        :page.sync="pager.offset"
        :limit.sync="pager.limit"
        @pagination="getList"
      />
    </div>
    <eval-import-drawer
      ref="importDrawer"
      :title="$t('pc_gwnl_eval_introductThirdAssessment')"
      fetch-temp
      :import-methods="importMethods"
      :id="id"
      interfaceStr="third"
      :btn-disabled="btnDisabled"
      @getTemplate="getTemplate"
      @success="search"
    />
    <selectRelation
      v-if="isDoing"
      :id="evaluationId"
      ref="refRelation"
      :relation-visible="relationVisible"
      :old-ass-datas="oldAssDatas"
      :is-save="isSave"
      @relationClose="relationClose"
      @prevRelation="prevRelation"
      @relationNext="relationNext"
    />
    <selectMessage
      :message-visible="messageVisible"
      :add-datas="addDatas"
      @messageClose="messageClose"
      @prevMessage="prevMessage"
    />
    <eval-import-drawer
      ref="importRelation"
      :title="$t('pc_eval_detail_impor_evaluatio_personnel')"
      :temp-url="tempUrl"
      :import-methods="importMethods1"
      :id="id"
      show-notify
      i18n
      @success="search"
    />
    <!-- 催促评估-非自评 -->
    <urge-person
      :visible.sync="personVisible"
      :userIds="getHurryDatas()"
      isMgmt
      @urge-success="onUrgeSuccess" />
    <!-- 催促评估-自评 -->
    <yxt-drawer
      :title="$t('pc_gwnl_eval_btn_urgeEval') /** 催促评估 */"
      :visible.sync="hurryDrawer"
      class="yxtbiz-eval-drawer-public"
      size="480px"
    >
      <yxt-radio v-model="hurryDrawerRadio" label="1">{{ $t('pc_gwnl_eval_useDefaultTemplate') /** 使用默认通知模板 */ }}</yxt-radio>
      <div class="mt16">
        <yxt-radio v-model="hurryDrawerRadio" label="2">
          {{ $t('pc_gwnl_eval_usePersonalizedTemplates') /** 使用个性通知模板 */ }}
          <span
            v-if="hurryDrawerRadio === '2'"
            class="pl16 color-primary-6"
            @click="showTemplate(tCode)"
          >{{ $t('pc_eval_urge_toast') /** 催促通知 */ }}</span>
        </yxt-radio>
      </div>
      <div slot="footer">
        <yxt-button plain @click="hurryDrawer = false;">{{ $t('pc_eval_default') /** 取消 */ }}</yxt-button>
        <yxt-button type="primary" :disabled="isPurryUp" @click="hurryComBtn">{{ $t('pc_eval_issure') /** 确定 */ }}</yxt-button>
      </div>
    </yxt-drawer>
    <yxtbiz-i18n-custom-template
      :code="tCode"
      :target-id="evaluationId || id"
      :visible.sync="visible[tCode]"
    />
    <!-- 设置评估关系 -->
    <yxtbiz-eval-relation
      :visible.sync="setRelationVisible"
      :evalId="id"
      :detail="evalInfo"
      :functionCode="functionCode"
      :evaluatorDataPermission="evaluatorDataPermission"
      @refresh-list="refreshListAndUpdate" />
    <!-- 分配份额人员 -->
    <yxt-drawer
      :visible.sync="occShareVisible"
      class="eval-occ-share__drawer yxtbiz-eval-drawer-public"
      size="960px"
      destroy-on-close
      :before-close="done => done(true)"
    >
      <div slot="title">
        <span>{{ $t('pc_eval_select_quota_person' /* 选择占用份额的人员 */) }}</span>
        <span class="ml16 standard-size-14 color-gray-7 yxt-weight-4">
          {{ $t('pc_gwnl_eval_remainingAvailableQuota', { count: copies }) }}
        </span>
      </div>
      <SelectOccShare ref="occShare" :evaluationId="evaluationId" :copies="copies" />

      <div slot="footer">
        <yxt-button plain @click="closeOccShareDrawer">
          {{ $t('pc_gwnl_global_msg_cancel').d('取消') }}
        </yxt-button>
        <yxt-button
          type="primary"
          :loading="occShareLoading"
          @click="confirmOccShareDrawer">
          {{ $t('pc_eval_issure') }}
        </yxt-button>
      </div>
    </yxt-drawer>

    <!-- 异常设置无效抽屉 -->
    <yxt-drawer
      :visible.sync="exceptionDrawer"
      class="eval-occ-share__drawer yxtbiz-eval-drawer-public"
      :title="$t('pc_eval_invalid_setting' /* 异常设置无效 */)"
      size="640px"
      destroy-on-close
      :label-tooltip="$t('pc_biz_eval_set_invalid_answer')"
      :before-close="done => done(true)"
    >
      <yxt-tree
        ref="exceptionTree"
        :data="exceptionRelationList"
        show-checkbox
        wrap-width="100%"
        node-key="id"
        default-expand-all
        :default-checked-keys="['root']">
      </yxt-tree>
      <div slot="footer">
        <yxt-button plain @click="closeExceptionDrawer">
          {{ $t('pc_gwnl_global_msg_cancel').d('取消') }}
        </yxt-button>
        <yxt-button
          type="primary"
          :loading="exceptionBtnLoading"
          @click="confirmExceptionDrawer">
          {{ $t('pc_eval_issure') }}
        </yxt-button>
      </div>
    </yxt-drawer>
  </div>
</template>

<script>
import { importEvalRelation, exportTrackPersons, postExportSummary, exportTrackSkillDetail, postExportOriginal, downloadDetailInfo, postExportBeiReason, postExportBeiRecord, sendQuestionnaireReport, downloadQuestionnaireReport, getEvalTemplate, importEval, sendTrackUrgeList, getTrackPersons, exportUserMsg, downProtsZip, resetPersonal, deleteEvalusers, getAvailableNum, postRemoveQuota, postSetQuota, postBatchEvaluationValid } from 'packages/eval-track/service.js';
import DeptCell from './DeptCell.vue';
import SelectTarget from './SelectTarget.vue';
import SelectRelation from './SelectRelation';
import SelectMessage from './SelectMessage.vue';
import TrackPerson from './TrackPerson.vue';
import SelectOccShare from './SelectOccShare.vue';
import handleError from 'packages/eval-create-drawer/src/handleError.js';
import { checkOpenPlatform, dateToUnified, routerToUrl } from 'packages/eval-track/core/utils.js';
import { TemplateFileEnums } from 'packages/eval-track/core/const.js';
import EvalSelectDepartmentTree from 'packages/eval-create-drawer/src/components/eval-select-thirdparty-user/src/components/eval-select-departmenttree/src/main.vue';
import down from 'packages/eval-track/mixins/down.js';
import UrgePerson from './UrgePerson.vue';
import { SOURCE_CODE } from '../configs/source';
import EvalPager from 'packages/eval-create-drawer/src/components/eval-pager/src/main.vue';
import commonUtil from 'yxt-biz-pc/packages/common-util';
import { mutations, state } from '../store';
import EvalImportDrawer from 'packages/eval-create-drawer/src/components/eval-import-drawer/src/main.vue';
import Dept8Dot from './Dot.vue';
import { dealLineFeed } from 'packages/arrange-track/src/core/utils.js';
const lastCreationDate = new Date('2022/03/11 00:00').getTime();

export default {
  components: {
    DeptCell,
    SelectTarget,
    SelectRelation,
    SelectMessage,
    TrackPerson,
    EvalSelectDepartmentTree,
    UrgePerson,
    EvalPager,
    EvalImportDrawer,
    Dept8Dot,
    SelectOccShare
  },
  mixins: [handleError, down],
  inject: ['isExternal', 'functionCode', 'deptDataPermissionCode', 'evaluatorDataPermission', 'id'],
  props: {
    sourceTypeList: {
      type: Array
    },
    scene: {
      type: Number // 评估方案场景(1-胜任力 2-人格 3-通用)
    },
    type: {
      type: Number // 1-行为,2-问卷,3-人格,4-多次行为评估
    },
    evalInfo: {
      type: Object,
      default: () => {
        return {};
      }
    },
    detail: {
      type: Object,
      default: () => {
        return {};
      }
    },
    evaluationId: {
      type: String
    },
    // 是否招聘场景下
    isRecruit: {
      type: Boolean,
      default: () => {
        return false;
      }
    },
    isSave: {
      type: Boolean,
      default: true
    },
    // 是否是多次行为评估
    multiBehavior: {
      type: Number,
      default: 0 // 0-单次行为评估 1-多次行为评估/主  2-3 多次行为评估/子
    }
  },
  data() {
    // 0-禁用,1-启用,2-已删除
    const statusList = [
      {
        label: this.$t('pc_gwnl_global_enabled').d('已启用'),
        value: 1,
        type: 'success'
      }, {
        label: this.$t('pc_gwnl_global_disabled').d('已禁用'),
        value: 0,
        type: 'danger'
      },
      {
        label: this.$t('pc_gwnl_global_deleted').d('已删除'),
        value: 2,
        type: 'failure'
      }];

    this.occShareList = [
      {
        label: this.$t('pc_eval_quota_unoccupied').d('未占用'),
        value: 0
      }, {
        label: this.$t('pc_eval_quota_occupied').d('已占用'),
        value: 1
      }
    ];
    return {
      SOURCE_CODE,
      isPurryUp: false,
      visible: {
        gwnl_eval_remind: false,
        talent_tourist_eval_notify: false
      }, // 个性模板配置弹框显示
      hurryDrawer: false, // 催促消息设置-抽屉
      hurryDrawerRadio: '1', // 1: 使用默认模版 2:使用个性模版
      hurryType: 0, // 0-催促评估，1-催促全部
      assess: 'peer',
      source_code: this.$route.query.source || '',
      yxtLang: window.localStorage.getItem('yxtLang') || 'zh',
      tempUrl: '',
      status: +this.$route.query.status, // 状态
      loading: false,
      pager: {
        total: 0,
        limit: 20,
        offset: 0
      }, // 分页
      params: {
        keyword: '',
        orderBy: 'evaluationFinishRate',
        direction: 'desc',
        udpStatus: '',
        quotaEnabled: ''
      },
      tableData: [],
      multipleSelection: [],
      urgeBtnDisable: false,
      btnDisabled: false,
      bathVisible: false,
      exportVisible: false,
      urgeVisible: false,
      downIconClass: 'yxt-icon-arrow-down yxt-icon--right',
      upIconClass: 'yxt-icon-arrow-up yxt-icon--right',
      templatePath: {
        zh: {
          self: '导入自评人员模板',
          peer: '导入评估人员模板'
        },
        ha: {
          self: '导入自评人员模板（繁体）',
          peer: '导入评估人员模板（繁体）'
        },
        en: {
          self: 'selfevaluationtemplate',
          peer: 'importevaluatorstemplate'
        }
      },
      statusOptions: {
        0: { label: this.$t('pc_gwnl_global_lbl_notGenerated' /* 未生成 */), value: 0, type: 'custom', customColor: '#d9d9d9' },
        1: { label: this.$t('pc_gwnl_global_lbl_notGenerated' /* 未生成 */), value: 1, type: 'custom', customColor: '#d9d9d9' },
        2: { label: this.$t('pc_gwnl_eval_generating' /* 生成中 */), value: 2, type: 'custom', customColor: '#1890FF' },
        3: { label: this.$t('pc_gwnl_eval_reportGenerated' /* 报告已生成 */), value: 3, type: 'success' },
        4: { label: this.$t('pc_gwnl_eval_generatedUnsuccessfully' /* 生成失败 */), value: 4, type: 'danger' },
        5: { label: this.$t('pc_gwnl_eval_generatedUnsuccessfully' /* 生成失败 */), value: 5, type: 'danger' },
        // 7: { label: this.$t('pc_gwnl_eval_dataGenerated' /* 数据已生成 */), value: 7, type: 'success' }
        7: { label: this.$t('pc_gwnl_eval_generating' /* 生成中 */), value: 7, type: 'custom', customColor: '#1890FF' }
      },
      statusList,
      statusMap: new Map(statusList.map(d => [d.value, d])),
      clickBtnDownloadId: '',
      clickBtnIssueId: '',
      relationVisible: false,
      assessorDatas: [],
      oldAssDatas: [],
      assessorCount: 0,
      relationPrev: false,
      isSelfeval: false,
      isDingDing: false,
      isPdf: false,
      messageVisible: false,
      addDatas: [],
      chargeSurveyId: '',
      departmentIds: [],
      selectUsers: [],
      stateMap: [
        {
          label: this.$t('pc_eval_lbl_multi_status0' /* 待下发 */),
          value: 0,
          type: 'primary'
        }, {
          label: this.$t('pc_gwnl_eval_sended' /* 已下发 */),
          value: 1,
          type: 'success'
        },
        {
          label: this.$t('pc_eval_lbl_multi_status2' /* 无需参与 */),
          value: 2,
          type: 'failure'
        }],

      isSelfAssess: false, // 是否是自评
      personVisible: false,
      setRelationVisible: false,
      occShareVisible: false, // 份额抽屉
      copies: 0, // 剩余可分配份额
      occShareLoading: false,
      exceptionAnswer: this.$t('pc_eval_stat_evaluation_result').replace(/\\r\\n/g, '\r\n').replace(/\\ r \\ n/g, '\r\n'),
      exceptionDrawer: false, // 异常无效抽屉
      exceptionBtnLoading: false // 异常无效抽屉确定按钮loading
    };
  },
  computed: {
    tCode() {
      const isZhaoPin = [4, 5].includes(this.evalPurpose);
      return isZhaoPin ? 'talent_tourist_eval_notify' : 'gwnl_eval_remind';
    },
    clickBtnDownload() {
      return function(item) {
        if (item.evaluationUserId === this.clickBtnDownloadId) {
          return true;
        } else {
          return false;
        }
      };
    },
    clickBtnIssue() {
      return function(item) {
        if (item.evaluationUserId === this.clickBtnIssueId) {
          return true;
        } else {
          return false;
        }
      };
    },

    // 1-行为,2-问卷,3-人格,4-多次行为评估
    // 不算倍智的工具
    isSurveyTools() {
      return this.type === 2;
    },

    // 展示作答检测列
    showExceptionAnswerColumn() {
      return this.hasQuestionnaire && !this.isBei && this.isSurveyTools;
    },

    hasQuestionnaire() {
      // 通用评估 或 胜任力评估的问卷评估
      return this.scene === 3 || (this.scene === 1 && this.isSurveyTools);
    },
    notStarte() {
      // 通用评估与胜任力评估的未开始状态的跟踪。催促评估、批量下发、导出数据都不能操作
      return (this.scene === 1 || this.scene === 3) && this.status === 1;
    },
    // 评估状态(0-未发布;1-已发布,进行中;2-已结束;3-已延期)
    isDoing() {
      return this.processEvalStatus || this.detail.evaluationStatus === 3;
    },
    // 通过参数控制按钮是否展示
    showAddEvalUser() {
      const createTime = new Date(dateToUnified(this.evalInfo.createTime)).getTime();
      if (this.processEvalStatus && createTime < lastCreationDate && this.evalInfo.scene === 1) {
        return true;
      }

      if (this.sourceTypeList.indexOf(this.detail.sourceType) !== -1) {
        return false;
      }

      return this.detail.evaluationStatus !== 3;
    },
    // 是否已经收集用户基本信息
    isCollectionItemSet() {
      return Number(this.evalInfo.collectionItemSet);
    },
    showDeleteEvalUser() {
      // 只要是外部创建的，就不可以删除
      if (this.sourceTypeList.includes(this.detail.sourceType)) {
        return false;
      }

      const createTime = new Date(dateToUnified(this.evalInfo.createTime)).getTime();
      if (this.processEvalStatus && createTime < lastCreationDate && this.evalInfo.scene === 1) {
        return true;
      }
      if (!(this.processEvalStatus && this.sourceTypeList.indexOf(this.detail.sourceType) !== -1)) {
        return true;
      }
      return false;
    },

    batchButton() {
      const batchButtonList = [];

      if (this.isDoing) {
        if (this.pandianSource) {
          // 解除占用
          if (this.chargeSurveyId) {
            batchButtonList.push({ name: 'pc_eval_quota_release', method: this.releaseQuota });
          }
        } else {
          // 批量删除
          if (this.showDeleteEvalUser) {
            batchButtonList.push({ name: 'pc_gwnl_global_batchDelete', method: this.batchDelete });
          }

          // 导入人员
          if (this.showAddEvalUser && this.evalPurpose !== 4 && this.evalPurpose !== 5) {
            batchButtonList.push({ name: 'pc_gwnl_eval_batch_Import_personnel', method: this.showImportRelation });
          }
        }
      }

      if (this.detail.personalReport && !this.isDingDing && this.isPdf) {
        // 批量下发
        batchButtonList.push({ name: 'pc_gwnl_eval_batchIssue', method: this.batchDownload });

        // 批量下载报告
        if (this.isBei) {
          batchButtonList.push({ name: 'pc_eval_detail_batchdownloadreport', method: this.batchDownloadReport });
        }
      }

      // 重新生成报告
      if (((this.detail.produceType && this.overEvalStatus) || this.detail.produceType === 0) && !this.isBei && this.detail.personalReport) {
        batchButtonList.push({ name: 'pc_gwnl_eval_regenerate_report', method: this.regenerateReport });
      }

      // 异常设置无效
      if (this.showExceptionAnswerColumn) {
        batchButtonList.push({
          name: 'pc_eval_invalid_setting',
          method: this.openExceptionDrawer
        });
      }

      return batchButtonList;
    },

    $isChinese() {
      return ['zh', 'ha'].includes(commonUtil.getLanguage());
    },

    // 未发布的测评
    notPublishEvalStatus() {
      return this.detail.evaluationStatus === -1;
    },

    // 未开始的测评
    notStartEvalStatus() {
      return this.detail.evaluationStatus === 0;
    },

    // 进行中的测评
    processEvalStatus() {
      return this.detail.evaluationStatus === 1;
    },

    // 已结束的测评
    overEvalStatus() {
      return this.detail.evaluationStatus === 2;
    },
    evalPurpose() {
      return state.evalInfo.evalPurpose;
    },

    multipleEvalUserIds() {
      return (this.multipleSelection || []).map(item => item.evaluationUserId);
    },

    // 盘点的来源才展示份额和设置评估关系
    pandianSource() {
      return this.isExternal && this.detail.sourceType === 2;
    },

    isBei() {
      return this.detail.surveySource === 4 || this.detail.surveySource === 5;
    },

    beiOperateWidth() {
      return this.$isChinese ? '226' : '340';
    },

    // 评估关系 & 自定义评估关系集合
    exceptionRelationList() {
      const { detail } = this;
      // 处理detail默认为空的情况
      if (!Object.keys(detail).length) return [];

      const dimensionInfoBeans = [
        { enabled: detail.enabledSelf, label: this.$t('pc_gwnl_chart_type_selfLevel'), type: 1 },
        { enabled: detail.enabledLeader, label: this.$t('pc_gwnl_chart_type_leaderLevel'), type: 2 },
        { enabled: detail.enabledEqual, label: this.$t('pc_gwnl_chart_type_equalLevel'), type: 3 },
        { enabled: detail.enabledSubordinate, label: this.$t('pc_gwnl_chart_type_subordinateLevel'), type: 4 }
      ];

      return [{
        id: 'root',
        label: this.$t('pc_biz_udp_lbl_selectAll' /* 全选 */),
        children: [
          ...dimensionInfoBeans.filter(item => item.enabled).map(item => {
            return {
              id: item.type,
              label: item.label
            };
          }),
          ...(detail.customDimensionInfoBeans || []).map(item => {
            return {
              id: item.relationType,
              label: item.dimensionName
            };
          })
        ]
      }];
    },

    // 异常是否可以操作, 是否都是正常答卷, 是否都是无效问卷
    exceptionCanOperate() {
      return this.multipleSelection.every(item => !item.hasExceptionAnswer);
    }
  },
  watch: {
    detail: {
      handler(val) {
        this.isPdf = val.isPdf;
        const isSelf = val.selfAssess === 1 && val.peerAssess !== 1;
        this.isSelfAssess = val.enabledSelf === 1 && val.weightSelf === 100;
        if (!isSelf) {
          this.isSelfeval = true;
          mutations.stateTrackCreat({
            isNext: true,
            closeMessage: true
          });
        } else {
          this.isSelfeval = false;
        }
        const startTime = new Date(val.startTime).getTime();
        const now = new Date().getTime();
        if (startTime > now) {
          this.status = 1;
        }
        // 问卷来源 0-自建 1-北极星题库 2-北极星问卷
        // charged 是否收费 0-免费 1-收费
        if ([1, 2, 4, 5].includes(val.surveySource) && this.evalInfo.charged === 1) {
          this.chargeSurveyId = val.surveyId;
        }

        this.$nextTick(() => {
          this.tempUrl = this.filterRelation(this.detail)
            ? TemplateFileEnums.evalRelationSelf
            : TemplateFileEnums.evalRelation;
        });
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.isDingDing = checkOpenPlatform();
  },
  mounted() {
    this.isPdf = this.detail.isPdf;
    this.search();
  },
  methods: {
    dealLineFeed,
    showTemplate(name) {
      this.visible[`${name}`] = true;
    },
    filterRelation(detail) {
      // 员工自评，上级评估，平级评估，下级评估
      if (detail.enabledSelf === 1 && (detail.enabledLeader !== 1 && detail.enabledEqual !== 1 && detail.enabledSubordinate !== 1)) {
        return true;
      } else {
        return false;
      }
    },
    importMethods1(file) {
      const formdata = new FormData();
      formdata.append('fileId', file.id);
      formdata.append('customization', file.customization ? file.customization : 0);
      formdata.append('enabledNotify', file.enabledNotify);
      return importEvalRelation(formdata, this.evaluationId || this.id);
    },
    showImportRelation() {
      this.$refs.importRelation.show();
    },
    // 添加评估人
    async changeDatas(datas, failedCount) {
      this.oldAssDatas = this.tableData;
      this.relationVisible = this.isSelfeval;
      await this.$refs.refRelation.changeDatas(datas, failedCount);
      await this.changeSwitch();
    },
    changeSwitch() {
      this.relationPrev = false;
    },
    // 批量下载报告
    batchDownloadReport() {
      debugger;
      if (this.multipleSelection.length === 0) {
        this.$message.warning(this.$t('pc_gwnl_global_msg_notSelectUser'));
        return;
      }
      // 3-生成完成,不是3就要提示“存在测评未完成的评估人，无法导出数据。”
      if (this.multipleSelection.some(item => item.reportStatus !== 3)) {
        this.$message.warning(this.$t('pc_eval_detail_batchdownloadreport_tip').d('存在测评未完成的评估人，无法导出数据。'));
        return;
      }
      const data = {
        evaluationId: this.evaluationId,
        evaluationUserIds: this.multipleEvalUserIds
      };
      downProtsZip(data).then(res => {
        this.goDownloadCenter();
      }).catch(() => {
        const error = this.$t('pc_gwnl_eval_msg_reportGeneratedUnsuccessfully');
        this.$message.error(error);
      });
    },
    // 重新生成报告
    regenerateReport() {
      const data = {
        evaluationId: this.evaluationId,
        evaluationUserIds: []
      };
      // 已结束才可以生产报告
      if (this.detail.produceType) {
        this.$confirm(this.$t('pc_gwnl_eval_all_regenerate_report'), this.$t('pc_gwnl_global_tips'), {
          confirmButtonText: this.$t('pc_gwnl_global_msg_determine'),
          cancelButtonText: this.$t('pc_gwnl_global_msg_cancel'),
          type: 'warning'
        }).then(() => {
          this.tableData.forEach(item => {
            data.evaluationUserIds.push(item.evaluationUserId);
          });
          resetPersonal(data).then(res => {
            this.search();
          }).catch(err => {
            this.handleError(err);
          });
        });
      } else {
        // 进行中可以生产报告
        if (this.multipleSelection && this.multipleSelection.length !== 0) {
          let finish = 0;
          let notFinish = 0;
          let notEmptyFinish = 0;
          let emptyFinish = 0;
          this.multipleSelection.forEach(item => {
            if (item.evaluationFinishRate === 1) finish += 1;
            if (item.evaluationFinishRate < 1) notEmptyFinish += 1;
            if (item.evaluationFinishRate > 0) notFinish += 1;
            if (item.evaluationFinishRate === 0) emptyFinish += 1;
          });
          if (this.overEvalStatus && notFinish) {
            this.$confirm(this.$t('pc_gwnl_eval_select_regenerate_report'), this.$t('pc_gwnl_global_tips'), {
              confirmButtonText: this.$t('pc_gwnl_global_msg_determine'),
              cancelButtonText: this.$t('pc_gwnl_global_msg_cancel'),
              type: 'warning'
            }).then(() => {
              this.multipleSelection.forEach(item => {
                data.evaluationUserIds.push(item.evaluationUserId);
              });
              resetPersonal(data).then(res => {
                this.search();
                this.$message.success(this.$t(`${notFinish}${this.$t('pc_gwnl_eval_regenerate_report_success')}，${emptyFinish}${this.$t('pc_gwnl_eval_regenerate_report_fail')}`));
              }).catch(err => {
                this.handleError(err);
              });
            });
          } else if (!this.overEvalStatus && finish) {
            this.$confirm(this.$t('pc_gwnl_eval_select_regenerate_report'), this.$t('pc_gwnl_global_tips'), {
              confirmButtonText: this.$t('pc_gwnl_global_msg_determine'),
              cancelButtonText: this.$t('pc_gwnl_global_msg_cancel'),
              type: 'warning'
            }).then(() => {
              this.multipleSelection.forEach(item => {
                data.evaluationUserIds.push(item.evaluationUserId);
              });
              resetPersonal(data).then(res => {
                this.search();
                this.$message.success(this.$t(`${finish}${this.$t('pc_gwnl_eval_regenerate_report_success')}，${notEmptyFinish}${this.$t('pc_gwnl_eval_regenerate_report_fail')}`));
              }).catch(err => {
                this.handleError(err);
              });
            });
          } else {
            this.$message.warning(this.$t('pc_gwnl_eval_notfinish_regenerate_report'));
          }
        } else {
          this.$message.warning(this.$t('pc_gwnl_eval_select_regenerate_report_user'));
        }
      }
    },
    // 批量删除
    batchDelete() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning(this.$t('pc_gwnl_eval_selectDelete'));
      } else {
        this.$confirm(this.$t('pc_gwnl_eval_isDeleteAssessor'), this.$t('pc_gwnl_global_tips'), {
          confirmButtonText: this.$t('pc_gwnl_global_msg_determine'),
          cancelButtonText: this.$t('pc_gwnl_global_msg_cancel'),
          type: 'warning'
        }).then(() => {
          const datas = [];
          this.multipleSelection.forEach(item => {
            if (item.evaluationFinishRate === 0) datas.push({ userId: item.userId });
          });
          if (datas.length) {
            deleteEvalusers(this.evaluationId, { datas }).then(res => {
              this.search();
            }).catch(err => {
              this.handleError(err);
            });
          }
        });
      }
    },
    batchDownload() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning(this.$t('pc_gwnl_eval_selectIssue'));
        return;
      }

      if (this.status === 3) {
        this.$confirm(this.$t('pc_gwnl_eval_successDownIssue'), this.$t('pc_gwnl_global_tips'), {
          confirmButtonText: this.$t('pc_gwnl_global_msg_determine'),
          cancelButtonText: this.$t('pc_gwnl_global_msg_cancel'),
          type: 'warning'
        }).then(() => {
          this.batchsendQuestionnaireReport(this.multipleEvalUserIds);
        });
      } else {
        this.$confirm(this.$t('pc_gwnl_eval_isIssueAssessor'), this.$t('pc_gwnl_global_tips'), {
          confirmButtonText: this.$t('pc_gwnl_global_msg_determine'),
          cancelButtonText: this.$t('pc_gwnl_global_msg_cancel'),
          type: 'warning'
        }).then(() => {
          this.batchsendQuestionnaireReport(this.multipleEvalUserIds);
        });
      }
    },
    batchsendQuestionnaireReport(result) {
      sendQuestionnaireReport(result).then(res => {
        switch (res.reportStatus) {
          case 1:
            // 待生成报告，请稍后重试
            this.$message.warning(this.$t('pc_gwnl_eval_msg_waitToGenerateReport'));
            break;
          case 2:
            // 报告生成中，请稍后重试
            this.$message.warning(this.$t('pc_gwnl_eval_msg_reportGenterating'));
            break;
          case 3:
            // 发送成功
            this.$message.success(this.$t('pc_gwnl_eval_msg_sendSuccessfully'));
            break;
          case 6:
            this.$message.success(res.resultMsg);
            break;
          default:
            // 生成失败
            this.$message.error(this.$t('pc_gwnl_eval_generatedUnsuccessfully'));
        }
      }).catch(err => {
        this.handleError(err);
      });
    },
    // 自评添加成功
    addSuccess(val) {
      if (val) this.search();
    },
    // 关闭评估人页面
    relationClose(val) {
      this.relationVisible = val.isClose;
      if (val && val.isAdd) {
        this.search();
      }
      mutations.stateTrackCreat({
        isNext: true,
        closeMessage: true
      });
    },
    prevRelation(val, data = []) {
      this.relationPrev = val;
      if (val) this.selectUsers = data;
    },
    relationNext(val) {
      const addCount = val.newAddDatas.length;
      if (addCount === 0) {
        this.$alert(this.$t('pc_gwnl_eval_not_add'), this.$t('pc_gwnl_global_tips'), {
          confirmButtonText: this.$t('pc_gwnl_global_msg_known'),
          type: 'error'
        });
      } else {
        this.messageVisible = val.isNext;
        this.addDatas = val.newAddDatas;
      }
    },
    sendQuestionnaireReport(row) {
      this.clickBtnIssueId = row.evaluationUserId;
      sendQuestionnaireReport(row.evaluationUserId).then(res => {
        if (res.reportStatus === 1) {
          this.$message.warning(this.$t('pc_gwnl_eval_msg_waitToGenerateReport'));
        } else if (res.reportStatus === 2) {
          row.reportStatus = 2;
          this.$message.warning(this.$t('pc_gwnl_eval_msg_reportGenterating'));
        } else if (res.reportStatus === 3) {
          row.reportStatus = 3;
          this.$message.success(this.$t('pc_gwnl_eval_msg_sendSuccessfully'));
        } else {
          row.reportStatus = 4;
          this.$message.error(this.$t('pc_gwnl_eval_msg_sendUnsuccessfully'));
        }
        this.clickBtnIssueId = '';
      }).catch((err) => {
        this.handleError(err);
        this.clickBtnIssueId = '';
      });
    },
    downloadQuestionnaireReport(row) {
      this.clickBtnDownloadId = row.evaluationUserId;
      downloadQuestionnaireReport(row.evaluationUserId).then(res => {
        if (res.reportStatus === 1) {
          this.$message.warning(this.$t('pc_gwnl_eval_msg_waitToGenerateReport' /* 待生成报告，请稍后重试 */));
        } else if (res.reportStatus === 2) {
          row.reportStatus = 2;
          this.$message.warning(this.$t('pc_gwnl_eval_msg_reportGenterating' /* 报告生成中，请稍后重试 */));
        } else if (res && res.fileUrl && res.reportStatus === 3) {
          row.reportStatus = 3;
          this.goDownloadCenter();
        } else {
          row.reportStatus = res.reportStatus === 7 ? 7 : 4;
          this.$message.error(this.$t('pc_gwnl_eval_msg_reportGeneratedUnsuccessfully' /* 报告生成失败 */));
        }
        this.clickBtnDownloadId = '';
      }).catch((err) => {
        this.handleError(err);
        this.clickBtnDownloadId = '';
      });
    },
    view(row) {
      routerToUrl({
        path: '/eval/personneldetail',
        query: {
          id: row.evaluationId,
          type: this.type,
          userId: row.userId,
          from: this.$route.query.from,
          scene: this.scene,
          purpose: this.evalInfo.evalPurpose,
          noSelfeval: this.isSelfeval ? 1 : 0,
          sourceurl: this.$route.query.sourceurl,
          mube: this.multiBehavior,
          ...(this.$route.query.isVisit ? { isVisit: '1' } : {})
        }
      }, true);
    },
    importEval() {
      this.$refs.importDrawer.show();
    },
    getTemplate() {
      this.btnDisabled = true;
      getEvalTemplate(this.evaluationId).then(res => {
        if (res) {
          const { protocol, host } = window.location;
          window.open(`${protocol}//${host}/down/#/download`);
          this.btnDisabled = false;
        }
      }).catch((err) => {
        this.handleError(err);
        this.btnDisabled = false;
      });
    },
    importMethods(file) {
      const formdata = new FormData();
      formdata.append('fileId', file.id);
      return importEval(this.evaluationId, formdata);
    },
    // 催促评估 && 催促全部 下拉菜单
    hurryChange(command) {
      switch (command) {
        case '1':
          this.sendUrge(0);// 当表达式的结果等于 value1 时，则执行该代码
          break;
        case '2':
          this.sendUrgeAll(1); // 当表达式的结果等于 value2 时，则执行该代码
          break;
        default :
          this.sendUrge(0); // 如果没有与表达式相同的值，则执行该代码
      }
    },
    sendUrgeAndOther() {
      this.personVisible = true;
    },

    onUrgeSuccess() {
      this.$refs.multipleTable.clearSelection();
    },

    // 催促评估
    sendUrge(type) {
      const selectedList = this.multipleSelection;
      // 请至少选择一位学员
      if (selectedList.length === 0) {
        this.$message.warning(this.$t('pc_gwnl_eval_msg_selectLeastTrainee'));
        return;
      }
      // 当前选择的人员全部提交了，不需要催促哦
      const result = selectedList.every(value => value.evaluationFinishRate === 1);
      if (result) {
        this.$message.warning(this.$t('pc_eval_tip_all_personnel_submitted' /* 当前选择的人员全部提交了，不需要催促哦 */));
        return;
      }

      this.isSelfAssess ? this.sendUrgeAll(type) : this.sendUrgeAndOther();
    },
    // 催促全部
    sendUrgeAll(type) {
      if (this.evalInfo.finishPercent < 1) {
        this.hurryDrawer = true;
        this.hurryType = type;
        this.hurryDrawerRadio = '1';
      } else {
        this.$message.warning(this.$t('pc_eval_tip_all_submitted_no_reminder' /* 所有人都提交了，不需要催促哦 */));
      }
    },
    // 确定催促
    hurryComBtn() {
      this.isPurryUp = true;
      const data = {
        customization: this.hurryDrawerRadio === '1' ? '0' : '1', // 是否使用个性通知模板
        datas: this.hurryType ? [] : this.getHurryDatas(), // 增量成功添加后的人员信息
        isUrge: this.hurryType // 是否催促全部
      };
      sendTrackUrgeList(this.evaluationId, data).then(res => {
        this.$message.success(this.$t('pc_gwnl_global_msg_urgeSuccess'));
        this.$refs.multipleTable.clearSelection();
        this.urgeBtnDisable = false;
      }).catch(err => {
        this.urgeBtnDisable = false;
        this.handleError(err);
      }).finally(() => {
        this.hurryDrawer = false;
        this.isPurryUp = false;
      });
    },
    // datas数据筛选
    getHurryDatas() {
      return this.multipleSelection
        .filter(item => item.evaluationFinishRate !== 1)
        .map(item => ({ userId: item.userId }));
    },
    search(type) {
      this.pager.offset = 0;
      // 多次行为评估切换tab,清除记录
      if (type === 'toggle') {
        this.params = {
          keyword: '',
          orderBy: 'evaluationFinishRate',
          direction: 'desc',
          udpStatus: ''
        };
        this.departmentIds = [];
      }
      this.getList();
    },
    getList() {
      if (this.loading) return;
      if (!this.evaluationId) return;
      this.loading = true;
      this.params.keyword = this.params.keyword.trim();
      const data = {
        deptIds: this.departmentIds,
        evaluationId: this.evaluationId,
        keyword: this.params.keyword,
        udpStatus: this.params.udpStatus === '' ? -1 : this.params.udpStatus,
        direction: this.params.direction,
        orderBy: this.params.orderBy,
        quotaEnabled: this.params.quotaEnabled
      };
      const params = {
        limit: this.pager.limit,
        offset: this.pager.offset
      };
      getTrackPersons(data, params).then(res => {
        this.pager.total = res.paging.count;
        this.tableData = res.datas;
        this.loading = false;
      }).catch((err) => {
        this.loading = false;
        this.handleError(err);
      });
    },
    // 导出收集的员工信息
    exportUserMsg() {
      const _data = {
        evaluationId: this.evaluationId
      };
      exportUserMsg(_data).then(() => {
        this.goDownloadCenter();
      }).catch((err) => {
        this.handleError(err);
      });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleSortChange({ column, prop, order }) {
      this.params.orderBy = prop;

      if (!order) {
        this.params.orderBy = '';
        this.params.direction = 'desc';
      } else {
        this.params.direction = order;
      }
      this.search();
    },
    // 修改原来的报告下载禁用规则
    // 只有【生成完成】才可以下载，其余的情况都不允许
    // reportStatus - 报告生成状态(0,1-待生成，2-生成中, 3-生成完成, 4-生成失败, 5-永久失败)
    downloadBtnDisabledComputed(row) {
      // if (!(row.evaluationFinishRate === 1 || (this.overEvalStatus && row.evaluationFinishRate > 0))) {
      //   return true;
      // }
      // if (row.reportStatus === 5) {
      //   return true;
      // }
      // return false;
      return row.reportStatus !== 3;
    },
    messageClose(val) {
      this.messageVisible = val.isClose;
    },
    prevMessage(val) {
      this.relationVisible = val;
    },
    selectDept(depts) {
      this.departmentIds = depts.map(dept => dept.id);
      this.search();
    },
    exportFnc(type) {
      if (this.tableData.length === 0) {
        this.$message.warning(this.$t('pc_gwnl_global_msg_noData'));
        return;
      }
      const ajaxFnc = {
        person: exportTrackPersons,
        summary: postExportSummary,
        skillDetail: exportTrackSkillDetail,
        original: postExportOriginal,
        detailInfo: downloadDetailInfo,
        reason: postExportBeiReason,
        record: postExportBeiRecord
      };
      const data = {
        evaluationId: this.evaluationId,
        keyword: this.params.keyword.trim(),
        udpStatus: this.params.udpStatus === '' ? -1 : this.params.udpStatus,
        direction: this.params.direction,
        orderBy: this.params.orderBy,
        deptIds: this.departmentIds,
        quotaEnabled: this.params.quotaEnabled
      };
      ajaxFnc[type](data).then(() => {
        this.goDownloadCenter();
      }).catch((err) => {
        this.handleError(err);
      });
    },
    handleMubeSelect(row) {
      return !(this.multiBehavior > 0 && (row.state === 0 || row.state === 2));
    },
    getFinishScore(state, rate) {
      let rateNum = '';
      if (this.multiBehavior) {
        rateNum = state === 1 ? (rate >= 0 ? Math.floor(rate * 10000) / 100 + '%' : '--') : '--';
      } else {
        rateNum = rate >= 0 ? Math.floor(rate * 10000) / 100 + '%' : '--';
      }
      return rateNum;
    },

    setEvalRelation() {
      this.setRelationVisible = true;
    },

    refreshListAndUpdate() {
      this.getList();
      this.$emit('update-basic-info');
    },

    // 设置分配测评份额
    setEvalShare() {
      if (!this.chargeSurveyId) return;

      getAvailableNum(this.chargeSurveyId).then((res) => {
        this.copies = res.data;
        this.occShareVisible = true;
      }).catch((err) => {
        this.handleError(err);
      });
    },

    closeOccShareDrawer() {
      this.occShareVisible = false;
    },

    getUserCheckAndData(selectedList) {
      // 请选择需要解除占用份额的被评估人
      if (selectedList.length === 0) {
        this.$message.warning(this.$t('pc_eval_msg_release_stu'));
        return false;
      }

      const datas = selectedList.map(item => {
        return {
          userId: item.userId
        };
      });

      return datas;
    },

    confirmOccShareDrawer() {
      const selectedList = this.$refs.occShare && this.$refs.occShare.selectedList;
      const datas = this.getUserCheckAndData(selectedList);
      if (!datas) return;

      if (selectedList.length > ~~this.copies) {
        // 选择学员数量超过剩余可用额度
        this.$message.warning(this.$t('pc_eval_quota_exceedsCopies'));
        return;
      }

      if (this.occShareLoading) return;
      this.occShareLoading = true;
      postSetQuota(this.evaluationId, { datas }).then(() => {
        this.closeOccShareDrawer();
        this.search();
        this.$emit('update-basic-info');
      }).catch(err => {
        this.handleError(err);
      }).finally(() => {
        this.occShareLoading = false;
      });
    },

    // 解除份额的占用
    releaseQuota() {
      const selectedList = this.multipleSelection;
      const datas = this.getUserCheckAndData(selectedList);
      if (!datas) return;

      this.$confirm(this.$t('pc_biz_eval_msg_release_quota_tips'), this.$t('pc_biz_lbl_tip' /* 提示 */), {
        type: 'warning'
      }).then(() => {
        postRemoveQuota(this.evaluationId, { datas }).then(() => {
          this.search();
          this.onUrgeSuccess();
          this.$emit('update-basic-info');
        }).catch(err => {
          this.handleError(err);
        });
      }).catch(() => {});
    },

    closeExceptionDrawer() {
      this.exceptionDrawer = false;
    },

    openExceptionDrawer() {
      // 请选择需要解除占用份额的被评估人
      if (this.multipleSelection.length === 0) {
        this.$message.warning(this.$t('pc_gwnl_global_msg_notSelectUser'));
        return;
      }

      if (this.exceptionCanOperate) {
        this.$message.warning(this.$t('pc_biz_eval_msg_no_invalid_answer' /* 当前暂无异常答卷 */));
        return;
      }

      this.exceptionDrawer = true;
    },

    confirmExceptionDrawer() {
      const ecr = (this.$refs.exceptionTree && this.$refs.exceptionTree.getCheckedNodes()) || [];
      if (ecr.length === 0) {
        this.$message.warning(this.$t('pc_eval_select_relation'));
        return;
      }

      const exceptionCurrentRelation = ecr.filter(item => item.id !== 'root').map(item => item.id);
      if (exceptionCurrentRelation.length === 0) {
        this.$message.warning(this.$t('pc_eval_select_relation'));
        return;
      }

      if (this.exceptionBtnLoading) return;
      this.exceptionBtnLoading = true;

      const data = {
        evaluationId: this.id,
        relationTypes: exceptionCurrentRelation,
        evalUserIds: this.multipleEvalUserIds,
        isValid: 0 // 是否有效 1有效 0无效
      };
      postBatchEvaluationValid(data).then(() => {
        // 答卷已被设为无效，如符合生成报告的条件，请到本测评跟踪页为员工重新生成报告。
        this.$message.success(this.$t('pc_eval_report_invalid_regenerate'));
        this.closeExceptionDrawer();
        this.search();
        this.onUrgeSuccess();
      }).catch(this.handleError).finally(() => {
        this.exceptionBtnLoading = false;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.yxtbiz-eval-detail {
  .quesBehave_header {
    display: flex;
    flex-wrap: wrap;
  }

  .table_wrapper_search {
    display: flex;
    flex-grow: 0;
    flex-shrink: 0;
    max-width: 100%;

    .search-input {
      width: 240px;
    }
  }

  .eval-detail__export-dropdown {
    .yxt-dropdown {
      vertical-align: top;
    }

    .yxt-dropdown + .yxt-dropdown {
      margin-left: 15px;
    }

    .yxt-icon--right {
      font-size: 12px;
    }
  }

  ::v-deep .yxt-form-item__tooltip svg {
    padding-right: 0 !important;
  }

  .yxt-form-item__tooltip svg {
    padding-right: 0 !important;
  }

  .eval-occ-share {
    &__drawer {
      ::v-deep .yxt-drawer__body {
        padding: 0 !important;
      }
    }
  }
}
</style>
