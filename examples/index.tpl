<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="utf-8">
    <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <link rel="stylesheet" href="//at.alicdn.com/t/font_137970_p1tpzmomxp9cnmi.css">
    <!-- <link rel=“mask-icon” href="https://raw.githubusercontent.com/ElemeFE/element/dev/examples/assets/images/element-logo-small.svg" color="#409EFF"> -->
    <link rel="stylesheet" href="//shadow.elemecdn.com/npm/highlight.js@9.3.0/styles/color-brewer.css">
    <script src='https://media-phx.yunxuetang.com.cn/static/vue/index.dev.js'></script>
    <script
        src="https://api-fecenter.yunxuetang.com.cn/feConfig/runtime/appname/yxt-biz-pc/env/tf-tc-01/nodeenv/production">
    </script>
    <link rel="stylesheet" href="https://media-phx.yunxuetang.com.cn/common/basepc/yxt-pc/lib/theme-chalk/index.css" />
    <link rel="stylesheet" href="https://media-phx.yunxuetang.com.cn/common/basepc/yxt-pc/lib/theme-chalk/icon.css" />
    <link rel="stylesheet"
        href="https://media-phx.yunxuetang.com.cn/common/basepc/yxt-pc/lib/front-theme-chalk/index.css" />
    <link rel="stylesheet"
        href="https://media-phx.yunxuetang.com.cn/common/basepc/yxt-pc/lib/front-theme-chalk/icon.css" />
    <script src='https://media-phx.yunxuetang.com.cn/common/basepc/yxt-pc/lib/index.js'></script>
    <script type="text/javascript" src="https://images.yxt.com/npm/echarts-5.2.2/dist/echarts.min.js"></script>
    <script type="text/javascript" src="https://images.yxt.com/npm/antv/g2/4.1.37/dist/g2.min.js"></script>
    <script type="text/javascript" src="https://images.yxt.com/npm/antv/pkg/_antv.data-set-0.10.2/dist/data-set.min.js"></script>
    <title>Yxt-biz-PC组件库</title>
    <meta name="description" content="Yxt-biz-PC组件库" />
</head>

<body>
    <script>
        if (!window.Promise) {
            if (!Array.prototype.fill) {
                Object.defineProperty(Array.prototype, 'fill', {
                    value: function(value) {
                        var O = Array(value);
                        var len = O.length >>> 0;
                        var start = arguments[1];
                        var relativeStart = start >> 0;
                        var k = relativeStart < 0 ?
                            Math.max(len + relativeStart, 0) :
                            Math.min(relativeStart, len);
                        var end = arguments[2];
                        var relativeEnd = end === undefined ?
                            len : end >> 0;
                        var final = relativeEnd < 0 ?
                            Math.max(len + relativeEnd, 0) :
                            Math.min(relativeEnd, len);
                        while (k < final) {
                            O[k] = value;
                            k++;
                        }
                        return O;
                    }
                });
                Object.defineProperty(Object, 'assign', {
                    value: function assign(target, varArgs) { // .length of function is 2
                        'use strict';
                        if (target == null) { // TypeError if undefined or null
                            throw new TypeError('Cannot convert undefined or null to object');
                        }

                        var to = Object(target);

                        for (var index = 1; index < arguments.length; index++) {
                            var nextSource = arguments[index];

                            if (nextSource != null) { // Skip over if undefined or null
                                for (var nextKey in nextSource) {
                                    // Avoid bugs when hasOwnProperty is shadowed
                                    if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {
                                        to[nextKey] = nextSource[nextKey];
                                    }
                                }
                            }
                        }
                        return to;
                    },
                    writable: true,
                    configurable: true
                });
                Object.defineProperty(Array.prototype, 'includes', {
                    value: function(searchElement, fromIndex) {

                        // 1. Let O be ? ToObject(this value).
                        if (this == null) {
                            throw new TypeError('"this" is null or not defined');
                        }

                        var o = Object(this);

                        // 2. Let len be ? ToLength(? Get(O, "length")).
                        var len = o.length >>> 0;

                        // 3. If len is 0, return false.
                        if (len === 0) {
                            return false;
                        }

                        // 4. Let n be ? ToInteger(fromIndex).
                        //    (If fromIndex is undefined, this step produces the value 0.)
                        var n = fromIndex | 0;

                        // 5. If n ≥ 0, then
                        //  a. Let k be n.
                        // 6. Else n < 0,
                        //  a. Let k be len + n.
                        //  b. If k < 0, let k be 0.
                        var k = Math.max(n >= 0 ? n : len - Math.abs(n), 0);

                        // 7. Repeat, while k < len
                        while (k < len) {
                            // a. Let elementK be the result of ? Get(O, ! ToString(k)).
                            // b. If SameValueZero(searchElement, elementK) is true, return true.
                            // c. Increase k by 1.
                            // NOTE: === provides the correct "SameValueZero" comparison needed here.
                            if (o[k] === searchElement) {
                                return true;
                            }
                            k++;
                        }

                        // 8. Return false
                        return false;
                    }
                });
            }
            document.write(
                '<script src="//cdn.jsdelivr.net/npm/es6-promise@4.1.1/dist/es6-promise.min.js"><\/script><script>ES6Promise.polyfill()<\/script>'
            )
        }
    </script>
    <div id="app"></div>
    <% if (process.env.NODE_ENV==='production' ) { %>
        <script src="//shadow.elemecdn.com/app/element/highlight.pack.b1f71b31-3c07-11e9-ba1a-55bba1877129.js"></script>
        <% } %>
</body>

</html>
