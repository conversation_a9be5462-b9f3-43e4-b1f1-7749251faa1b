## QuestionSelector 选择试题

### 初始化

:::demo
```html
<template>
  <yxt-button type="primary" @click="drawer = true">选择试题</yxt-button>
  <div>{{ datas }}</div>
  <div class="template3">
    <yxt-drawer
      title="选择试题"
      size="960px"
      :visible="drawer"
      :destroy-on-close="true"
      :before-close="close"
    >
      <yxtbiz-question-selector ref="quesSelector" :subjectivity="true" :quesList="quesList"  ></yxtbiz-question-selector>
      <div slot="footer">
        <yxt-button plain @click="close">取消</yxt-button>
        <yxt-button type="primary" @click="submit">确定</yxt-button>
      </div>
    </yxt-drawer>
  </div>
</template>
<script>
export default {
  name: 'selectQuestion',
  data() {
    return {
      drawer: false,
      datas: [],
      quesList: [{'id': '042349c6-fa71-40d6-8a9b-39e8028ae3c8', 'summary': '【示例需删除】骆驼分为双峰驼和单峰驼两种。', 'quesType': 0, 'status': 2, 'type': 2, 'levelType': 0, 'score': 0, 'disabled': 0, 'auditInfo': null, 'libId': '7f140b65-3419-46c4-ad58-00fca7e26da9', 'pointId': null, 'pointName': '默认考点', 'pointNames': '默认考点', 'duplicate': 0, 'libName': '富文本音视频批阅练习', 'orderIndex': 3, 'createUserId': null, 'createFullname': null, 'catalogName': null, 'createTime': '2024-03-06 17:42:48', 'updateTime': '2024-03-06 17:42:48', 'shared': 0, 'sharedUsername': null, 'sharedFullname': null, 'sourceId': null, 'content': '【示例需删除】骆驼分为双峰驼和单峰驼两种。', 'explainText': '动物类', 'isMultiMediaType': 0, 'subQuesSenior': 0, 'createUserName': null, 'jumpLibId': null}]
    };
  },
  beforeCreate() {
  },
  created() {
  },
  methods: {
    // 提交
    submit() {
      this.datas = this.$refs.quesSelector.getSelected();
      this.close();
    },
    close() {
      this.drawer = false;
    }
  }
};
</script>

```
:::

### 游戏化场景排除多媒体类题目及主观题

:::demo
```html
<template>
  <yxt-button type="primary" @click="drawer = true">选择试题</yxt-button>
  <div>{{ datas }}</div>
  <div class="template3">
    <yxt-drawer
      title="选择试题"
      size="960px"
      :visible="drawer"
      :before-close="close"
    >
      <yxtbiz-question-selector ref="quesSelector" :subjectivity="false" :multimedia="false"></yxtbiz-question-selector>
      <div slot="footer">
        <yxt-button plain @click="close">取消</yxt-button>
        <yxt-button type="primary" @click="submit">确定</yxt-button>
      </div>
    </yxt-drawer>
  </div>
</template>
<script>
export default {
  name: 'selectQuestion',
  data() {
    return {
      drawer: false,
      datas: []
    };
  },
  beforeCreate() {
  },
  created() {
  },
  methods: {
    // 提交
    submit() {
      this.datas = this.$refs.quesSelector.getSelected();
      this.close();
    },
    close() {
      this.drawer = false;
    }
  }
};
</script>

```
:::

### 只需要部分类型题目

:::demo
```html
<template>
  <yxt-button type="primary" @click="drawer = true">选择试题</yxt-button>
  <div>{{ datas }}</div>
  <div class="template3">
    <yxt-drawer
      title="选择试题"
      size="960px"
      :visible="drawer"
      :before-close="close"
    >
      <yxtbiz-question-selector ref="quesSelector" :includeQuesTypes="[0,2]"></yxtbiz-question-selector>
      <div slot="footer">
        <yxt-button plain @click="close">取消</yxt-button>
        <yxt-button type="primary" @click="submit">确定</yxt-button>
      </div>
    </yxt-drawer>
  </div>
</template>
<script>
export default {
  name: 'selectQuestion',
  data() {
    return {
      drawer: false,
      datas: []
    };
  },
  beforeCreate() {
  },
  created() {
  },
  methods: {
    // 提交
    submit() {
      this.datas = this.$refs.quesSelector.getSelected();
      this.close();
    },
    close() {
      this.drawer = false;
    }
  }
};
</script>

```
:::

### Attributes

| 参数    | 说明           | 类型   | 可选值 | 默认值 |
| ------- | -------------- | ------ | ------ | ------ |
| subjectivity | 是否包含主观题（填空、问答、组合），非时只包含客观题（单选、多选、判断） | Boolean |        | true     |
| multimedia | 是否包含多媒体内容的题目（视频、音频、图片） | Boolean |        | true     |
| includeQuesTypes | 包含的题型。 试题题型 0-单选题 1-多选题 2-判断题 3-填空题 4-问答题 5-组合题。 例子：只需要单选、判断时传 [0，2] | Array |        | []     |
| quesList | 已选择的试题信息，会默认打开试题蓝 | Array |        | []     |
| basket | 是否打开试题蓝） | Boolean |        | false     |


### Method

| 方法名 | 说明             | 返回值                             |
| ------ | ---------------- | ------------------------------------ |
| getSelected | 获取已选择的试题列表 | [{id,summary,quesType ...详见试题的关键属性}] |

### 试题的关键属性
|属性|描述|
|---|---|
|summary|试题内容，是去除了富文本标记后的纯文本内容。（只保留富文本内容的1333长度，并不能直接用于学员端展示）|
|type|试题题型： 0-单选题 1-多选题 2-判断题 3-填空题 4-问答题 5-组合题 |
|quesType|试题内容类型： -1-普通 2-音频 3-视频|
|levelType|试题难度： 0-易 1-中 2-难 |
|libName|题库名称 |
|pointName|首个考核点名称 |
|pointNames|考核点名称集合，需要按照 ; 分割|
