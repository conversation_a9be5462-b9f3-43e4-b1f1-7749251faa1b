## EvalTrack 测评跟踪
### 组件介绍

:::demo

```html
<template>
  <div>
    <yxtbiz-eval-track id="d4f734e0-bdc7-426e-b61d-5bf59e97a89c" :isFixedTitle="false" isExternal />
  </div>
</template>
<script>

export default {
  data () {
    return {
      trackType: 1
    }
  }
}
</script>
```

:::


### Attributes

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
| ------ | -------- | -------- | -------- | -------- |
| id | 测评id | String | - | - |
| isFixedTitle | 是否固定标题栏 | Boolean | true/false | false |
| isExternal | 是否外部引用跟踪页 | Boolean | true/false | false |
| isAssessMgmt | 是否跳转评估管理页 | Boolean | true/false | false |
| functionCode | 导航权限code | String | - | sp_gwnl_eval_center |
| deptDataPermissionCode | 部门权限code | String | - | sp_eval_project_evalproject_deplist |
| evaluatorDataPermission | 添加人员权限code | String | - | sp_eval_project_evaluated_add_extent |

### Methods
| 方法名 | 说明               | 参数 |
| ------ | ------------------ | -------- |

### Events
| 事件名   | 说明     | 回调参数 |
| -------- | -------- | -------- |