{"name": "kng", "packages": ["complain", "comment", "select-kng-catalog", "watermark", "user-medal-tag", "image-viewer", "select-newkng", "tcm-select", "skip-task", "user-medal-dailog", "language-slot", "doc-viewer", "doc-player", "video", "practice", "gratuity", "ulcd", "select-kng", "select-ability-duty", "image-cropper", "tag", "richeditor", "teacher-selector", "teacher-selector-v2", "ability-selector", "talent-standard", "kng-scorm-player", "attachment-check", "supplier-select", "kng-points", "kng-operator", "open-download-tool", "audit-workflow-detail", "discuss-question", "discuss-speech", "course-player", "duty-selector", "type-search", "ai-hw-review", "lib-selector", "intelligent-questioning", "intelligent-questioning-tcm-sources", "tcm-practice-detail", "tcm-practice-view"], "directories": {"lib": "lib", "test": "__tests__"}, "files": ["lib"], "scripts": {"build:file": "node ../../build/lerna/build-entry.js $(basename $PWD)", "build:umd": "cross-env ../../node_modules/webpack/bin/webpack.js --config  ../../build/lerna.unpkg.js", "build": "npm run build:file && npm run build:umd", "analyze": "cross-env MOD=base1 report=yes webpack --config  ../../build/lerna.unpkg.js"}, "devDependencies": {"cross-env": "^3.1.3"}}