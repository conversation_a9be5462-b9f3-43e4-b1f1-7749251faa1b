{"name": "talent", "packages": ["exam-arrange-makeup", "ulcd", "certificate-selector", "exam-selector", "select-kng", "select-ability-duty", "teacher-selector", "survey-template", "amap", "richeditor", "tutor-selector", "file-join-knglib", "doc-viewer", "doc-player", "skip-task", "labelling", "image-cropper", "select-course", "user-selector-position", "teacher-subscribe-manage", "visitor-importor", "ability-preview", "model-selector", "polestar-radar", "skill-details", "skill-viewer", "trainings-standard", "project-selector", "create-project", "select-team", "select-quota", "eval-delay", "eval-create-drawer", "eval-import-eval-create", "eval-evaluator-table", "eval-user-management", "select-dimension", "dimension", "discuss-question", "discuss-speech", "course-player", "select-model", "talent-model", "talent-quota", "indicator-detail", "talent-standard", "talent-ability-trend", "talent-eval-trend", "eval-track", "eval-relation", "kng-operator", "performance-for-signup", "talent-calibration", "uacd-task-list", "uacd", "study-map-design-list", "arrange-track", "activity-outline"], "directories": {"lib": "lib", "test": "__tests__"}, "files": ["lib"], "scripts": {"build:file": "node ../../build/lerna/build-entry.js $(basename $PWD)", "build:umd": "cross-env ../../node_modules/webpack/bin/webpack.js --config  ../../build/lerna.unpkg.js", "build": "npm run build:file && npm run build:umd", "analyze": "cross-env MOD=base1 report=yes webpack --config  ../../build/lerna.unpkg.js"}, "devDependencies": {"cross-env": "^3.1.3"}}