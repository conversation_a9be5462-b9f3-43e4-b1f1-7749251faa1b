{"name": "ote", "packages": ["ability-selector", "skip-task", "certificate-preview", "question-preview", "visitor-importor", "exam-arrange-makeup", "certificate-selector", "exam-selector", "question-selector", "richeditor", "watermark", "auth-selector", "doc-viewer", "doc-player", "image-viewer", "attachment-check", "intelligent-questioning", "lib-selector", "arrange-statistics", "select-quota", "select-model", "ding-msg"], "directories": {"lib": "lib", "test": "__tests__"}, "files": ["lib"], "scripts": {"build:file": "node ../../build/lerna/build-entry.js $(basename $PWD)", "build:umd": "cross-env ../../node_modules/webpack/bin/webpack.js --config  ../../build/lerna.unpkg.js", "build": "npm run build:file && npm run build:umd", "analyze": "cross-env MOD=base1 report=yes webpack --config  ../../build/lerna.unpkg.js"}, "devDependencies": {"cross-env": "^3.1.3"}}