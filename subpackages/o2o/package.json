{"name": "o2o", "packages": ["doc-viewer", "doc-player", "attachment-check", "file-join-knglib", "tutor-selector", "survey-template", "exam-selector", "richeditor", "exam-arrange-makeup", "image-cropper", "tag", "ability-selector", "certificate-selector", "ulcd", "position-name", "hwk-template-selector", "select-kng", "select-ability-duty", "talent-standard", "skip-task", "teacher-level", "check-person-range", "exam-arrange-selector", "select-course", "teacher-selector", "amap", "teacher-subscribe-manage", "visitor-importor", "o2o-multi-evaluate-dialog", "user-medal-tag", "merge-evaluation", "image-viewer", "watermark", "kng-scorm-player", "complain", "language-slot", "gratuity", "course-player", "ai-project-assistant", "select-quota", "eval-delay", "eval-create-drawer", "eval-import-eval-create", "eval-evaluator-table", "eval-training-dialog", "eval-user-management", "ability-preview", "model-selector", "polestar-radar", "skill-details", "skill-viewer", "trainings-standard", "voice", "attachment-list", "arrange-statistics", "discuss-question", "discuss-speech", "ai-training-design", "aibox-unify", "meeting-room-reservation", "skill-matrix", "talent-quota", "indicator-detail", "talent-ability-trend", "talent-eval-trend", "talent-trainings-standard", "talent-model", "select-model", "performance-for-signup", "ai-hw-review", "ding-msg", "lib-selector", "intelligent-questioning", "intelligent-questioning-tcm-sources", "tcm-practice-detail", "tcm-practice-view", "practice"], "directories": {"lib": "lib", "test": "__tests__"}, "files": ["lib"], "scripts": {"build:file": "node ../../build/lerna/build-entry.js $(basename $PWD)", "build:umd": "cross-env ../../node_modules/webpack/bin/webpack.js --config  ../../build/lerna.unpkg.js", "build": "npm run build:file && npm run build:umd", "analyze": "cross-env MOD=base1 report=yes webpack --config  ../../build/lerna.unpkg.js"}, "devDependencies": {"cross-env": "^3.1.3"}}