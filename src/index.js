/* Automatically generated by './build/bin/build-entry.js' */

import NavTop from '../packages/nav-top/index.js';
import DeptTree from '../packages/dept-tree/index.js';
import PosTree from '../packages/pos-tree/index.js';
import NavLeft from '../packages/nav-left/index.js';
import NavMain from '../packages/nav-main/index.js';
import UserSelector from '../packages/user-selector/index.js';
import SingleUser from '../packages/single-user/index.js';
import UserGroupTree from '../packages/user-group-tree/index.js';
import CheckPersonRange from '../packages/check-person-range/index.js';
import AbilitySelector from '../packages/ability-selector/index.js';
import AssociatedTermsSelector from '../packages/associated-terms-selector/index.js';
import CheckSinglePosition from '../packages/check-single-position/index.js';
import FunsTree from '../packages/funs-tree/index.js';
import Upload from '../packages/upload/index.js';
import AuthSelector from '../packages/auth-selector/index.js';
import PersonSelector from '../packages/person-selector/index.js';
import SurveyTemplate from '../packages/survey-template/index.js';
import ExamSelector from '../packages/exam-selector/index.js';
import ExamArrange from '../packages/exam-arrange/index.js';
import ExamArrangeSelector from '../packages/exam-arrange-selector/index.js';
import TeacherSelector from '../packages/teacher-selector/index.js';
import TutorSelector from '../packages/tutor-selector/index.js';
import CertificateSelector from '../packages/certificate-selector/index.js';
import Qrcode from '../packages/qrcode/index.js';
import Complain from '../packages/complain/index.js';
import ImageCropper from '../packages/image-cropper/index.js';
import AreaSelect from '../packages/area-select/index.js';
import Richeditor from '../packages/richeditor/index.js';
import Tag from '../packages/tag/index.js';
import ImageViewer from '../packages/image-viewer/index.js';
import PlaceSelector from '../packages/place-selector/index.js';
import Amap from '../packages/amap/index.js';
import Personalitytemplate from '../packages/PersonalityTemplate/index.js';
import EnrollSettings from '../packages/enroll-settings/index.js';
import EnrollManage from '../packages/enroll-manage/index.js';
import EnrollDetail from '../packages/enroll-detail/index.js';
import Reward from '../packages/Reward/index.js';
import SelectKng from '../packages/select-kng/index.js';
import Watermark from '../packages/watermark/index.js';
import ExamArrangeMakeup from '../packages/exam-arrange-makeup/index.js';
import MsgCollector from '../packages/msg-collector/index.js';
import MsgCollectorV2 from '../packages/msg-collector-v2/index.js';
import MsgEditor from '../packages/msg-editor/index.js';
import MsgEditorV2 from '../packages/msg-editor-v2/index.js';
import DocViewer from '../packages/doc-viewer/index.js';
import Comment from '../packages/comment/index.js';
import Captcha from '../packages/captcha/index.js';
import NavTopStu from '../packages/nav-top-stu/index.js';
import NavFooter from '../packages/nav-footer/index.js';
import NavManageStore from '../packages/nav-manage-store/index.js';
import SelectKngCatalog from '../packages/select-kng-catalog/index.js';
import SelectKngCatelogSource from '../packages/select-kng-catelog-source/index.js';
import SelectKngSource from '../packages/select-kng-source/index.js';
import InfoSelector from '../packages/info-selector/index.js';
import Video from '../packages/video/index.js';
import StudyCenterNav from '../packages/study-center-nav/index.js';
import NavBreadcrumb from '../packages/nav-breadcrumb/index.js';
import PersonRangeSelector from '../packages/person-range-selector/index.js';
import AttachmentCheck from '../packages/attachment-check/index.js';
import Voice from '../packages/voice/index.js';
import PersonalCenterNav from '../packages/personal-center-nav/index.js';
import UploadImage from '../packages/upload-image/index.js';
import Search from '../packages/search/index.js';
import NavImmersive from '../packages/nav-immersive/index.js';
import NavTab from '../packages/nav-tab/index.js';
import SupportSidebar from '../packages/support-sidebar/index.js';
import VirtualList from '../packages/virtual-list/index.js';
import UserCenterNav from '../packages/user-center-nav/index.js';
import NavLeftStu from '../packages/nav-left-stu/index.js';
import Practice from '../packages/practice/index.js';
import Ulcd from '../packages/ulcd/index.js';
import CommonSelector from '../packages/common-selector/index.js';
import ImportProc from '../packages/import-proc/index.js';
import Breadcrumb from '../packages/breadcrumb/index.js';
import SelectNewkng from '../packages/select-newkng/index.js';
import SkipTask from '../packages/skip-task/index.js';
import CollegeSelector from '../packages/college-selector/index.js';
import TcmSelect from '../packages/tcm-select/index.js';
import NavLang from '../packages/nav-lang/index.js';
import I18nCustomTemplate from '../packages/i18n-custom-template/index.js';
import I18nLang from '../packages/i18n-lang/index.js';
import I18nInput from '../packages/i18n-input/index.js';
import I18nText from '../packages/i18n-text/index.js';
import CertificatePreview from '../packages/certificate-preview/index.js';
import UserMedalTag from '../packages/user-medal-tag/index.js';
import VisitorImportor from '../packages/visitor-importor/index.js';
import DeptManagerTree from '../packages/dept-manager-tree/index.js';
import AreaCodeSelect from '../packages/area-code-select/index.js';
import UserMedalDailog from '../packages/user-medal-dailog/index.js';
import QuestionSelector from '../packages/question-selector/index.js';
import QuestionPreview from '../packages/question-preview/index.js';
import Gratuity from '../packages/gratuity/index.js';
import FileJoinKnglib from '../packages/file-join-knglib/index.js';
import KngInfo from '../packages/kng-info/index.js';
import SelectCourse from '../packages/select-course/index.js';
import CreateLive from '../packages/create-live/index.js';
import SponsorChoose from '../packages/sponsor-choose/index.js';
import TeacherLevel from '../packages/teacher-level/index.js';
import Eco from '../packages/eco/index.js';
import GroupOrgSelect from '../packages/group-org-select/index.js';
import GroupSourceSelector from '../packages/group-source-selector/index.js';
import QueslibPreview from '../packages/queslib-preview/index.js';
import SelectTrainingProjects from '../packages/select-training-projects/index.js';
import SelectMaps from '../packages/select-maps/index.js';
import LiveSelector from '../packages/live-selector/index.js';
import OjtAudit from '../packages/ojt-audit/index.js';
import TeacherEnrollAudit from '../packages/teacher-enroll-audit/index.js';
import Signup from '../packages/signup/index.js';
import NewUlcdCom from '../packages/new-ulcd-com/index.js';
import NewUlcdSelect from '../packages/new-ulcd-select/index.js';
import Consult from '../packages/consult/index.js';
import CoursePlayer from '../packages/course-player/index.js';
import AsyncImportList from '../packages/async-import-list/index.js';
import ShopSelector from '../packages/shop-selector/index.js';
import AreaSelector from '../packages/area-selector/index.js';
import InspectionSelector from '../packages/inspection-selector/index.js';
import NewUlcdIdentify from '../packages/new-ulcd-identify/index.js';
import TitleUtil from '../packages/title-util/index.js';
import O2oTrainplanAudit from '../packages/o2o-trainplan-audit/index.js';
import GroupMember from '../packages/group-member/index.js';
import LanguageSlot from '../packages/language-slot/index.js';
import O2oEnrollAudit from '../packages/o2o-enroll-audit/index.js';
import ProjectSelector from '../packages/project-selector/index.js';
import MergeEvaluation from '../packages/merge-evaluation/index.js';
import O2oMultiEvaluateDialog from '../packages/o2o-multi-evaluate-dialog/index.js';
import RangeSelector from '../packages/range-selector/index.js';
import Rank from '../packages/rank/index.js';
import SelectRank from '../packages/select-rank/index.js';
import RuleCondition from '../packages/rule-condition/index.js';
import RuleCycle from '../packages/rule-cycle/index.js';
import RuleValidity from '../packages/rule-validity/index.js';
import AcEnrollAudit from '../packages/ac-enroll-audit/index.js';
import Labelling from '../packages/labelling/index.js';
import SurveySelector from '../packages/survey-selector/index.js';
import TeacherSalaryAudit from '../packages/teacher-salary-audit/index.js';
import TeacherRdSalaryAudit from '../packages/teacher-rd-salary-audit/index.js';
import TeacherEditAudit from '../packages/teacher-edit-audit/index.js';
import TeacherSubscribeAudit from '../packages/teacher-subscribe-audit/index.js';
import TeacherExperience from '../packages/teacher-experience/index.js';
import TeacherSubscribeManage from '../packages/teacher-subscribe-manage/index.js';
import NavTopWorkbench from '../packages/nav-top-workbench/index.js';
import BoardAudit from '../packages/board-audit/index.js';
import CircleAudit from '../packages/circle-audit/index.js';
import FaceSelector from '../packages/face-selector/index.js';
import RankList from '../packages/rank-list/index.js';
import RankSetter from '../packages/rank-setter/index.js';
import PracticeSelector from '../packages/practice-selector/index.js';
import CheckItemSelector from '../packages/check-item-selector/index.js';
import AcWorkAudit from '../packages/ac-work-audit/index.js';
import CheckListSelector from '../packages/check-list-selector/index.js';
import KngAuditContent from '../packages/kng-audit-content/index.js';
import KngCommentAuditContent from '../packages/kng-comment-audit-content/index.js';
import O2oEnrollProjectsetAudit from '../packages/o2o-enroll-projectset-audit/index.js';
import ProjectPlanSelect from '../packages/project-plan-select/index.js';
import HwkTemplateSelector from '../packages/hwk-template-selector/index.js';
import KngScormPlayer from '../packages/kng-scorm-player/index.js';
import TypeSearch from '../packages/type-search/index.js';
import FlipInfo from '../packages/flip-info/index.js';
import RegisterAudit from '../packages/register-audit/index.js';
import ProjectAudit from '../packages/project-audit/index.js';
import TcmSelectAc from '../packages/tcm-select-ac/index.js';
import AiRobot from '../packages/ai-robot/index.js';
import AiRobotDemo from '../packages/ai-robot-demo/index.js';
import AiRobotSearch from '../packages/ai-robot-search/index.js';
import MultiClassAudit from '../packages/multi-class-audit/index.js';
import CardConsumptAudit from '../packages/card-consumpt-audit/index.js';
import KngOperator from '../packages/kng-operator/index.js';
import KngPoints from '../packages/kng-points/index.js';
import IntelligentQuestioning from '../packages/intelligent-questioning/index.js';
import SelectKngCatalogV2 from '../packages/select-kng-catalog-v2/index.js';
import DeptTreeV2 from '../packages/dept-tree-v2/index.js';
import DeptTreeV3 from '../packages/dept-tree-v3/index.js';
import AiProjectAssistant from '../packages/ai-project-assistant/index.js';
import AbilityPreview from '../packages/ability-preview/index.js';
import SkillViewer from '../packages/skill-viewer/index.js';
import SkillDetails from '../packages/skill-details/index.js';
import PolestarRadar from '../packages/polestar-radar/index.js';
import TrainingsStandard from '../packages/trainings-standard/index.js';
import ModelSelector from '../packages/model-selector/index.js';
import EvalDelay from '../packages/eval-delay/index.js';
import EvalCreateDrawer from '../packages/eval-create-drawer/index.js';
import EvalEvaluatorTable from '../packages/eval-evaluator-table/index.js';
import EvalUserManagement from '../packages/eval-user-management/index.js';
import EvalImportEvalCreate from '../packages/eval-import-eval-create/index.js';
import EvalTrainingDialog from '../packages/eval-training-dialog/index.js';
import SupplierSelector from '../packages/supplier-selector/index.js';
import SupplierSelect from '../packages/supplier-select/index.js';
import UserSelectorPosition from '../packages/user-selector-position/index.js';
import ExtendFieldSelector from '../packages/extend-field-selector/index.js';
import MengniuAnnualAudit from '../packages/mengniu-annual-audit/index.js';
import MengniuMonthAudit from '../packages/mengniu-month-audit/index.js';
import LibSelector from '../packages/lib-selector/index.js';
import HourRecordAudit from '../packages/hour-record-audit/index.js';
import SelectTeam from '../packages/select-team/index.js';
import SelectDimension from '../packages/select-dimension/index.js';
import SparringProjectSelector from '../packages/sparring-project-selector/index.js';
import Dimension from '../packages/dimension/index.js';
import LabelAudit from '../packages/label-audit/index.js';
import KngScormCompleteStandard from '../packages/kng-scorm-complete-standard/index.js';
import ArrangeStatistics from '../packages/arrange-statistics/index.js';
import OpenDownloadTool from '../packages/open-download-tool/index.js';
import ApassExternalPersonnelSelect from '../packages/apass-external-personnel-select/index.js';
import AuditCenter from '../packages/audit-center/index.js';
import TmapAudit from '../packages/tmap-audit/index.js';
import AuditWorkflowDetail from '../packages/audit-workflow-detail/index.js';
import Aibox from '../packages/aibox/index.js';
import AiboxUnify from '../packages/aibox-unify/index.js';
import CategoryTree from '../packages/category-tree/index.js';
import CategorySelector from '../packages/category-selector/index.js';
import DocPlayer from '../packages/doc-player/index.js';
import TeacherSelectorV2 from '../packages/teacher-selector-v2/index.js';
import TutorSelectorV2 from '../packages/tutor-selector-v2/index.js';
import DiscussSpeech from '../packages/discuss-speech/index.js';
import DiscussQuestion from '../packages/discuss-question/index.js';
import AttachmentList from '../packages/attachment-list/index.js';
import AiTrainingDesign from '../packages/ai-training-design/index.js';
import FlipLeaveAudit from '../packages/flip-leave-audit/index.js';
import AttendLeaveAudit from '../packages/attend-leave-audit/index.js';
import ArrangeAudit from '../packages/arrange-audit/index.js';
import AiAssistant from '../packages/ai-assistant/index.js';
import FlowEditor from '../packages/flow-editor/index.js';
import FlowLog from '../packages/flow-log/index.js';
import MarkdownEditor from '../packages/markdown-editor/index.js';
import FlowVariable from '../packages/flow-variable/index.js';
import ImageCropperv2 from '../packages/image-cropperv2/index.js';
import DutySelector from '../packages/duty-selector/index.js';
import SelectAbilityDuty from '../packages/select-ability-duty/index.js';
import DeclarationHour from '../packages/declaration-hour/index.js';
import IndicatorDetail from '../packages/indicator-detail/index.js';
import SelectQuota from '../packages/select-quota/index.js';
import SelectModel from '../packages/select-model/index.js';
import TalentStandard from '../packages/talent-standard/index.js';
import ArrangeTrack from '../packages/arrange-track/index.js';
import EvalTrack from '../packages/eval-track/index.js';
import EvalRelation from '../packages/eval-relation/index.js';
import MeetingRoomReservation from '../packages/meeting-room-reservation/index.js';
import CsmAuthorize from '../packages/csm-authorize/index.js';
import ProcessNodeApaas from '../packages/process-node-apaas/index.js';
import ApplicationNodeApaas from '../packages/application-node-apaas/index.js';
import CreateProject from '../packages/create-project/index.js';
import DynamicMatchRule from '../packages/dynamic-match-rule/index.js';
import ActivityOutline from '../packages/activity-outline/index.js';
import UacdTaskList from '../packages/uacd-task-list/index.js';
import UacdStore from '../packages/uacd-store/index.js';
import Uacd from '../packages/uacd/index.js';
import ImPkg from '../packages/im-pkg/index.js';
import DimensionRule from '../packages/dimension-rule/index.js';
import TalentModel from '../packages/talent-model/index.js';
import TalentQuota from '../packages/talent-quota/index.js';
import AomProjectAudit from '../packages/aom-project-audit/index.js';
import SkillMatrix from '../packages/skill-matrix/index.js';
import TalentAbilityTrend from '../packages/talent-ability-trend/index.js';
import TalentEvalTrend from '../packages/talent-eval-trend/index.js';
import TalentAbilityTrendReport from '../packages/talent-ability-trend-report/index.js';
import TalentEvalTrendReport from '../packages/talent-eval-trend-report/index.js';
import TalentTrainingsStandard from '../packages/talent-trainings-standard/index.js';
import CapacityRankingList from '../packages/capacity-ranking-list/index.js';
import HomeBanner from '../packages/home-banner/index.js';
import HomeAppList from '../packages/home-app-list/index.js';
import HomeFuncsList from '../packages/home-funcs-list/index.js';
import RelevancePlan from '../packages/relevance-plan/index.js';
import FaceRecognition from '../packages/face-recognition/index.js';
import PerformanceForSignup from '../packages/performance-for-signup/index.js';
import RptSelectProject from '../packages/rpt-select-project/index.js';
import RptSelectKng from '../packages/rpt-select-kng/index.js';
import AiHwReview from '../packages/ai-hw-review/index.js';
import RptSelectPlan from '../packages/rpt-select-plan/index.js';
import TalentCalibration from '../packages/talent-calibration/index.js';
import TalentReportSelectProjects from '../packages/talent-report-select-projects/index.js';
import TalentSelectMap from '../packages/talent-select-map/index.js';
import IpaasTriggerEvent from '../packages/ipaas-trigger-event/index.js';
import IpaasExecuteAction from '../packages/ipaas-execute-action/index.js';
import StuNavControl from '../packages/stu-nav-control/index.js';
import DingMsg from '../packages/ding-msg/index.js';
import RptSelectArea from '../packages/rpt-select-area/index.js';
import RptSelectCheckItem from '../packages/rpt-select-check-item/index.js';
import RptSelectCheckList from '../packages/rpt-select-check-list/index.js';
import RptSelectInspection from '../packages/rpt-select-inspection/index.js';
import RptSelectShop from '../packages/rpt-select-shop/index.js';
import ArrangeSelectorPaas from '../packages/arrange-selector-paas/index.js';
import PracticeSelectorPaas from '../packages/practice-selector-paas/index.js';
import PlatformContacts from '../packages/platform-contacts/index.js';
import RptSelectLive from '../packages/rpt-select-live/index.js';
import TagSelector from '../packages/tag-selector/index.js';
import AiAnswerSetting from '../packages/ai-answer-setting/index.js';
import AiEvaluationReport from '../packages/ai-evaluation-report/index.js';
import StudyMapDesignList from '../packages/study-map-design-list/index.js';
import IntelligentQuestioningTcmSources from '../packages/intelligent-questioning-tcm-sources/index.js';
import TcmPracticeDetail from '../packages/tcm-practice-detail/index.js';
import TcmPracticeView from '../packages/tcm-practice-view/index.js';
import NioClassAudit from '../packages/nio-class-audit/index.js';
import TeamSelector from '../packages/team-selector/index.js';
import Api, { archiveApi, udpApi, oteApi, orginitApi, cerApi, qidaApi, commonApi, ccApi, fileApi, surveyApi, teApi, logApi, decorateApi, kngApi, knglibApi, enrollApi, sspApi, newsApi, o2oApi, bbsApi, utilityApi, bsetApi, searchApi, omsApi, msgApi, getMedia, getDomains, noticeApi, rewardApi, miscApi, auditCenterApi } from 'yxt-biz-pc/packages/api';
import { transformApiParams } from 'yxt-biz-pc/packages/api/configDomain.js';
import commonUtil from '../packages/common-util/index.js';
import navManageStore from '../packages/nav-manage-store/index.js';
import EcoEs from '../packages/eco';

// open-data: import
import yxtOpenData from 'yxt-open-data/es';
import yxtI18n from 'yxt-i18n/es';
import yxtFactor from 'yxt-factor/es';

import { isIE } from 'yxt-biz-pc/packages/common-util/utils.js';

const timezone = yxtI18n.timezone;
timezone.init();

const {UserName, DeptName, PositionName, OpenData, DdOpenData: OpenDataDd} = yxtOpenData;

const components = [
  NavTop,
  DeptTree,
  PosTree,
  NavLeft,
  NavMain,
  UserSelector,
  SingleUser,
  UserGroupTree,
  CheckPersonRange,
  AbilitySelector,
  AssociatedTermsSelector,
  CheckSinglePosition,
  FunsTree,
  Upload,
  AuthSelector,
  PersonSelector,
  SurveyTemplate,
  ExamSelector,
  ExamArrange,
  ExamArrangeSelector,
  TeacherSelector,
  TutorSelector,
  CertificateSelector,
  Qrcode,
  Complain,
  ImageCropper,
  AreaSelect,
  Richeditor,
  Tag,
  ImageViewer,
  PlaceSelector,
  Amap,
  Personalitytemplate,
  EnrollSettings,
  EnrollManage,
  EnrollDetail,
  Reward,
  SelectKng,
  Watermark,
  ExamArrangeMakeup,
  MsgCollector,
  MsgCollectorV2,
  MsgEditor,
  MsgEditorV2,
  DocViewer,
  Comment,
  Captcha,
  NavTopStu,
  NavFooter,
  SelectKngCatalog,
  SelectKngCatelogSource,
  SelectKngSource,
  InfoSelector,
  Video,
  StudyCenterNav,
  NavBreadcrumb,
  PersonRangeSelector,
  AttachmentCheck,
  Voice,
  PersonalCenterNav,
  UploadImage,
  Search,
  NavImmersive,
  NavTab,
  SupportSidebar,
  VirtualList,
  UserCenterNav,
  NavLeftStu,
  Practice,
  Ulcd,
  CommonSelector,
  OpenData,
  UserName,
  DeptName,
  ImportProc,
  Breadcrumb,
  SelectNewkng,
  SkipTask,
  CollegeSelector,
  TcmSelect,
  NavLang,
  I18nCustomTemplate,
  I18nLang,
  I18nInput,
  I18nText,
  CertificatePreview,
  OpenDataDd,
  PositionName,
  UserMedalTag,
  VisitorImportor,
  DeptManagerTree,
  AreaCodeSelect,
  UserMedalDailog,
  QuestionSelector,
  QuestionPreview,
  Gratuity,
  FileJoinKnglib,
  KngInfo,
  SelectCourse,
  CreateLive,
  SponsorChoose,
  TeacherLevel,
  Eco,
  GroupOrgSelect,
  GroupSourceSelector,
  QueslibPreview,
  SelectTrainingProjects,
  SelectMaps,
  LiveSelector,
  OjtAudit,
  TeacherEnrollAudit,
  Signup,
  NewUlcdCom,
  NewUlcdSelect,
  Consult,
  CoursePlayer,
  AsyncImportList,
  ShopSelector,
  AreaSelector,
  InspectionSelector,
  NewUlcdIdentify,
  O2oTrainplanAudit,
  GroupMember,
  LanguageSlot,
  O2oEnrollAudit,
  ProjectSelector,
  MergeEvaluation,
  O2oMultiEvaluateDialog,
  RangeSelector,
  Rank,
  SelectRank,
  RuleCondition,
  RuleCycle,
  RuleValidity,
  AcEnrollAudit,
  Labelling,
  SurveySelector,
  TeacherSalaryAudit,
  TeacherRdSalaryAudit,
  TeacherEditAudit,
  TeacherSubscribeAudit,
  TeacherExperience,
  TeacherSubscribeManage,
  NavTopWorkbench,
  BoardAudit,
  CircleAudit,
  FaceSelector,
  RankList,
  RankSetter,
  PracticeSelector,
  CheckItemSelector,
  AcWorkAudit,
  CheckListSelector,
  KngAuditContent,
  KngCommentAuditContent,
  O2oEnrollProjectsetAudit,
  ProjectPlanSelect,
  HwkTemplateSelector,
  KngScormPlayer,
  TypeSearch,
  FlipInfo,
  RegisterAudit,
  ProjectAudit,
  TcmSelectAc,
  AiRobot,
  AiRobotDemo,
  AiRobotSearch,
  MultiClassAudit,
  CardConsumptAudit,
  KngOperator,
  KngPoints,
  IntelligentQuestioning,
  SelectKngCatalogV2,
  DeptTreeV2,
  DeptTreeV3,
  AiProjectAssistant,
  AbilityPreview,
  SkillViewer,
  SkillDetails,
  PolestarRadar,
  TrainingsStandard,
  ModelSelector,
  EvalDelay,
  EvalCreateDrawer,
  EvalEvaluatorTable,
  EvalUserManagement,
  EvalImportEvalCreate,
  EvalTrainingDialog,
  SupplierSelector,
  SupplierSelect,
  UserSelectorPosition,
  ExtendFieldSelector,
  MengniuAnnualAudit,
  MengniuMonthAudit,
  LibSelector,
  HourRecordAudit,
  SelectTeam,
  SelectDimension,
  SparringProjectSelector,
  Dimension,
  LabelAudit,
  KngScormCompleteStandard,
  ArrangeStatistics,
  OpenDownloadTool,
  ApassExternalPersonnelSelect,
  AuditCenter,
  TmapAudit,
  AuditWorkflowDetail,
  Aibox,
  AiboxUnify,
  CategoryTree,
  CategorySelector,
  DocPlayer,
  TeacherSelectorV2,
  TutorSelectorV2,
  DiscussSpeech,
  DiscussQuestion,
  AttachmentList,
  AiTrainingDesign,
  FlipLeaveAudit,
  AttendLeaveAudit,
  ArrangeAudit,
  AiAssistant,
  FlowEditor,
  FlowLog,
  MarkdownEditor,
  FlowVariable,
  ImageCropperv2,
  DutySelector,
  SelectAbilityDuty,
  DeclarationHour,
  IndicatorDetail,
  SelectQuota,
  SelectModel,
  TalentStandard,
  ArrangeTrack,
  EvalTrack,
  EvalRelation,
  MeetingRoomReservation,
  CsmAuthorize,
  ProcessNodeApaas,
  ApplicationNodeApaas,
  CreateProject,
  DynamicMatchRule,
  ActivityOutline,
  UacdTaskList,
  Uacd,
  ImPkg,
  DimensionRule,
  TalentModel,
  TalentQuota,
  AomProjectAudit,
  SkillMatrix,
  TalentAbilityTrend,
  TalentEvalTrend,
  TalentAbilityTrendReport,
  TalentEvalTrendReport,
  TalentTrainingsStandard,
  CapacityRankingList,
  HomeBanner,
  HomeAppList,
  HomeFuncsList,
  RelevancePlan,
  FaceRecognition,
  PerformanceForSignup,
  RptSelectProject,
  RptSelectKng,
  AiHwReview,
  RptSelectPlan,
  TalentCalibration,
  TalentReportSelectProjects,
  TalentSelectMap,
  IpaasTriggerEvent,
  IpaasExecuteAction,
  StuNavControl,
  DingMsg,
  RptSelectArea,
  RptSelectCheckItem,
  RptSelectCheckList,
  RptSelectInspection,
  RptSelectShop,
  ArrangeSelectorPaas,
  PracticeSelectorPaas,
  PlatformContacts,
  RptSelectLive,
  TagSelector,
  AiAnswerSetting,
  AiEvaluationReport,
  StudyMapDesignList,
  IntelligentQuestioningTcmSources,
  TcmPracticeDetail,
  TcmPracticeView,
  NioClassAudit,
  TeamSelector
];

let loadingIndex = 0;
components.forEach(Component => {
  Component.mixins = Component.mixins || [];
  Component.mixins.push({
    created() {
      // 业务组件埋点统计
      // from：组件名
      // aspect：事件发生描述
      // version：组件库版本
      window.YxtFeLog && window.YxtFeLog.track('e_component', {
        properties: {
          from: Component.name,
          aspect: 'load',
          version: '1.23.0'
        }
      });
    }
  });
});

const setStaticCdnUrl = function(Vue) {
  try {
    if (Vue) {
      const baseCommon = (typeof window !== 'undefined' && window.feConfig && window.feConfig.common);
      Vue.prototype.$imagesBaseUrl = (baseCommon && window.feConfig.common.imagesBaseUrl) || 'https://images.yxt.com/';
      Vue.prototype.$staticBaseUrl = (baseCommon && window.feConfig.common.staticBaseUrl) || 'https://stc.yxt.com/';
    }
  } catch (e) {
    console.log(e);
  }
};

const logVersion = function() {
  const { log } = console;
  log('%cYXT-BIZ-PC 1.23.0', 'color: #fff; border-radius: 3px; padding: 3px 7px;background: linear-gradient(315deg, #fc5c7d 0%, #6a82fb 74%)');
};

const install = function(Vue, config = {}) { // config: {env: 'dev', domain: {orginit: ''}}
  setStaticCdnUrl(Vue);
  !isIE() && logVersion();

  const oldIndex = ++loadingIndex;
  const loadFun = function() {
    if (oldIndex !== loadingIndex) return;
    const options = transformApiParams(config);
    Api.setConfig(options);
    // xxSignup.Api.setConfig(options, options.source, options.domain);
    yxtI18n.Api.setConfig(options, options.source, options.domain);
    EcoEs.Api.setConfig({ ...options });
    yxtFactor.install(options);

    components.forEach(component => {
      component.name && Vue.component(component.name, component);
    });
    // signup: registry 废弃
    // xxSignup.install(Vue, {
    //   auditCenterApi
    // });
    // open-data: registry
    yxtOpenData.install(Vue, config);

    document.body ? commonUtil.bindImgPreviewer(document.body) : document.addEventListener('DOMContentLoaded', function() {
      commonUtil.bindImgPreviewer(document.body);
    });
    commonUtil.globalwaterInit && commonUtil.globalwaterInit.init();
  };
  // 组件内如果未传入domain 尝试自己获取
  try {
    if (!config.domain) {
      if (window.feConfig && window.feConfig.common) {
        config.domain = window.feConfig.common;
        config.env = window.feConfig.apiEnv;
        loadFun();
        return;
      }
      config.env = config.env || (window.feConfig && window.feConfig.apiEnv) || 'dev';
      // 尝试获取配置
      let ajax = new XMLHttpRequest();
      ajax.open('GET', 'https://api-fecenter.yunxuetang.com.cn/feConfig/runtime/original/appname/yxt-biz-pc/env/' + (config.env || 'dev') + '/nodeenv/production', true);
      ajax.send(null);
      ajax.onreadystatechange = function() {
        if (ajax.readyState === 4 && ajax.status === 200) {
          const json = JSON.parse(ajax. responseText);
          config.domain = (json.feConfig && json.feConfig.common) || {};
        }
      };
      ajax.onloadend = function() {
        loadFun();
      };
    } else {
      loadFun();
    }
  } catch (error) {
    loadFun();
  }
};

/* istanbul ignore if */
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue);
}

export {
  timezone
};

export default {
  version: '1.23.0',
  install,
  timezone,
  yxtOpenData,
  NavTop,
  DeptTree,
  PosTree,
  NavLeft,
  NavMain,
  UserSelector,
  SingleUser,
  UserGroupTree,
  CheckPersonRange,
  AbilitySelector,
  AssociatedTermsSelector,
  CheckSinglePosition,
  FunsTree,
  Upload,
  AuthSelector,
  PersonSelector,
  SurveyTemplate,
  ExamSelector,
  ExamArrange,
  ExamArrangeSelector,
  TeacherSelector,
  TutorSelector,
  CertificateSelector,
  Qrcode,
  Complain,
  ImageCropper,
  AreaSelect,
  Richeditor,
  Tag,
  ImageViewer,
  PlaceSelector,
  Amap,
  Personalitytemplate,
  EnrollSettings,
  EnrollManage,
  EnrollDetail,
  Reward,
  SelectKng,
  Watermark,
  ExamArrangeMakeup,
  MsgCollector,
  MsgCollectorV2,
  MsgEditor,
  MsgEditorV2,
  DocViewer,
  Comment,
  Captcha,
  NavTopStu,
  NavFooter,
  NavManageStore,
  SelectKngCatalog,
  SelectKngCatelogSource,
  SelectKngSource,
  InfoSelector,
  Video,
  StudyCenterNav,
  NavBreadcrumb,
  PersonRangeSelector,
  AttachmentCheck,
  Voice,
  PersonalCenterNav,
  UploadImage,
  Search,
  NavImmersive,
  NavTab,
  SupportSidebar,
  VirtualList,
  UserCenterNav,
  NavLeftStu,
  Practice,
  Ulcd,
  CommonSelector,
  OpenData,
  UserName,
  DeptName,
  ImportProc,
  Breadcrumb,
  SelectNewkng,
  SkipTask,
  CollegeSelector,
  TcmSelect,
  NavLang,
  I18nCustomTemplate,
  I18nLang,
  I18nInput,
  I18nText,
  CertificatePreview,
  OpenDataDd,
  PositionName,
  UserMedalTag,
  VisitorImportor,
  DeptManagerTree,
  AreaCodeSelect,
  UserMedalDailog,
  QuestionSelector,
  QuestionPreview,
  Gratuity,
  FileJoinKnglib,
  KngInfo,
  SelectCourse,
  CreateLive,
  SponsorChoose,
  TeacherLevel,
  Eco,
  GroupOrgSelect,
  GroupSourceSelector,
  QueslibPreview,
  SelectTrainingProjects,
  SelectMaps,
  LiveSelector,
  OjtAudit,
  TeacherEnrollAudit,
  Signup,
  NewUlcdCom,
  NewUlcdSelect,
  Consult,
  CoursePlayer,
  AsyncImportList,
  ShopSelector,
  AreaSelector,
  InspectionSelector,
  NewUlcdIdentify,
  TitleUtil,
  O2oTrainplanAudit,
  GroupMember,
  LanguageSlot,
  O2oEnrollAudit,
  ProjectSelector,
  MergeEvaluation,
  O2oMultiEvaluateDialog,
  RangeSelector,
  Rank,
  SelectRank,
  RuleCondition,
  RuleCycle,
  RuleValidity,
  AcEnrollAudit,
  Labelling,
  SurveySelector,
  TeacherSalaryAudit,
  TeacherRdSalaryAudit,
  TeacherEditAudit,
  TeacherSubscribeAudit,
  TeacherExperience,
  TeacherSubscribeManage,
  NavTopWorkbench,
  BoardAudit,
  CircleAudit,
  FaceSelector,
  RankList,
  RankSetter,
  PracticeSelector,
  CheckItemSelector,
  AcWorkAudit,
  CheckListSelector,
  KngAuditContent,
  KngCommentAuditContent,
  O2oEnrollProjectsetAudit,
  ProjectPlanSelect,
  HwkTemplateSelector,
  KngScormPlayer,
  TypeSearch,
  FlipInfo,
  RegisterAudit,
  ProjectAudit,
  TcmSelectAc,
  AiRobot,
  AiRobotDemo,
  AiRobotSearch,
  MultiClassAudit,
  CardConsumptAudit,
  KngOperator,
  KngPoints,
  IntelligentQuestioning,
  SelectKngCatalogV2,
  DeptTreeV2,
  DeptTreeV3,
  AiProjectAssistant,
  AbilityPreview,
  SkillViewer,
  SkillDetails,
  PolestarRadar,
  TrainingsStandard,
  ModelSelector,
  EvalDelay,
  EvalCreateDrawer,
  EvalEvaluatorTable,
  EvalUserManagement,
  EvalImportEvalCreate,
  EvalTrainingDialog,
  SupplierSelector,
  SupplierSelect,
  UserSelectorPosition,
  ExtendFieldSelector,
  MengniuAnnualAudit,
  MengniuMonthAudit,
  LibSelector,
  HourRecordAudit,
  SelectTeam,
  SelectDimension,
  SparringProjectSelector,
  Dimension,
  LabelAudit,
  KngScormCompleteStandard,
  ArrangeStatistics,
  OpenDownloadTool,
  ApassExternalPersonnelSelect,
  AuditCenter,
  TmapAudit,
  AuditWorkflowDetail,
  Aibox,
  AiboxUnify,
  CategoryTree,
  CategorySelector,
  DocPlayer,
  TeacherSelectorV2,
  TutorSelectorV2,
  DiscussSpeech,
  DiscussQuestion,
  AttachmentList,
  AiTrainingDesign,
  FlipLeaveAudit,
  AttendLeaveAudit,
  ArrangeAudit,
  AiAssistant,
  FlowEditor,
  FlowLog,
  MarkdownEditor,
  FlowVariable,
  ImageCropperv2,
  DutySelector,
  SelectAbilityDuty,
  DeclarationHour,
  IndicatorDetail,
  SelectQuota,
  SelectModel,
  TalentStandard,
  ArrangeTrack,
  EvalTrack,
  EvalRelation,
  MeetingRoomReservation,
  CsmAuthorize,
  ProcessNodeApaas,
  ApplicationNodeApaas,
  CreateProject,
  DynamicMatchRule,
  ActivityOutline,
  UacdTaskList,
  UacdStore,
  Uacd,
  ImPkg,
  DimensionRule,
  TalentModel,
  TalentQuota,
  AomProjectAudit,
  SkillMatrix,
  TalentAbilityTrend,
  TalentEvalTrend,
  TalentAbilityTrendReport,
  TalentEvalTrendReport,
  TalentTrainingsStandard,
  CapacityRankingList,
  HomeBanner,
  HomeAppList,
  HomeFuncsList,
  RelevancePlan,
  FaceRecognition,
  PerformanceForSignup,
  RptSelectProject,
  RptSelectKng,
  AiHwReview,
  RptSelectPlan,
  TalentCalibration,
  TalentReportSelectProjects,
  TalentSelectMap,
  IpaasTriggerEvent,
  IpaasExecuteAction,
  StuNavControl,
  DingMsg,
  RptSelectArea,
  RptSelectCheckItem,
  RptSelectCheckList,
  RptSelectInspection,
  RptSelectShop,
  ArrangeSelectorPaas,
  PracticeSelectorPaas,
  PlatformContacts,
  RptSelectLive,
  TagSelector,
  AiAnswerSetting,
  AiEvaluationReport,
  StudyMapDesignList,
  IntelligentQuestioningTcmSources,
  TcmPracticeDetail,
  TcmPracticeView,
  NioClassAudit,
  TeamSelector,
  commonUtil,
  Api,
  udpApi,
  oteApi,
  orginitApi,
  cerApi,
  qidaApi,
  commonApi,
  ccApi,
  fileApi,
  surveyApi,
  teApi,
  logApi,
  decorateApi,
  kngApi,
  knglibApi,
  enrollApi,
  sspApi,
  newsApi,
  o2oApi,
  bbsApi,
  utilityApi,
  bsetApi,
  searchApi,
  omsApi,
  msgApi,
  getMedia,
  getDomains,
  navManageStore,
  archiveApi,
  noticeApi,
  rewardApi,
  miscApi,
  auditCenterApi
};
