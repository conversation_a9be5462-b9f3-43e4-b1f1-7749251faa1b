/** Header Component */
import { YxtbizHeader } from './header';
import { YxtbizLeftmenu } from './leftmenu';
import { YxtbizFooter } from './footer';
import { YxtbizNavTop } from './nav-top';
import { YxtbizApi } from './api';
import { YxtbizDeptTree } from './dept-tree';
import { YxtbizPosTree } from './pos-tree';
import { YxtbizNavLeft } from './nav-left';

import { YxtbizNavMain } from './nav-main';
import { YxtbizUserGroupTree } from './user-group-tree';
import { YxtbizCheckPersonRange } from './check-person-range';
import { YxtbizCheckSinglePosition } from './check-single-position';
import { YxtbizFunsTree } from './funs-tree';

import { YxtbizUpload } from './upload';

import { YxtbizSurveyTemplate } from './survey-template';
import { YxtbizExamSelector } from './exam-selector';
import { YxtbizExamArrange } from './exam-arrange';
import { YxtbizExamArrangeSelector } from './exam-arrange-selector';
import { YxtbizTeacherSelector } from './teacher-selector';
import { YxtbizTutorSelector } from './tutor-selector';
import { YxtbizCertificateSelector } from './certificate-selector';
import { YxtbizQrcode } from './qrcode';
import { YxtbizComplain } from './complain';
import { YxtbizImageCropper } from './image-cropper';
import { YxtbizAreaSelect } from './area-select';
import { YxtbizRicheditor } from './richeditor';
import { YxtbizImageViewer } from './image-viewer';
import { YxtbizPlaceSelector } from './place-selector';
import { YxtbizAmap } from './amap';
import { YxtbizPersonalitytemplate } from './PersonalityTemplate';

import { YxtbizEnrollSettings } from './enroll-settings';
import { YxtbizReward } from './Reward';

import { YxtbizSelectKng } from './select-kng';
import { YxtbizWatermark } from './watermark';

import { YxtbizExamArrangeMakeup } from './exam-arrange-makeup';
import { YxtbizDocViewer } from './doc-viewer';
import { YxtbizComment } from './comment';
import { YxtbizNavTopStu } from './nav-top-stu';
import { YxtbizNavFooter } from './nav-footer';

import { YxtbizSelectKngCatalog } from './select-kng-catalog';
import { YxtbizSelectKngCatelogSource } from './select-kng-catelog-source';
import { YxtbizSelectKngSource } from './select-kng-source';

import { YxtbizInfoSelector } from './info-selector';

import { YxtbizVideo } from './video';
import { YxtbizNavBreadcrumb } from './nav-breadcrumb';
import { YxtbizPersonRangeSelector } from './person-range-selector';
import { YxtbizUploadImage } from './upload-image';
import { YxtbizVoice } from './voice';
import { YxtbizPersonalCenterNav } from './personal-center-nav';
import { YxtbizSearch } from './search';
import { YxtbizNavImmersive } from './nav-immersive';
import { YxtbizNavTab } from './nav-tab';
import { YxtbizSupportSidebar } from './support-sidebar';
import { YxtbizVirtualList } from './virtual-list';
import { YxtbizUserCenterNav } from './user-center-nav';
import { YxtbizNavLeftStu } from './nav-left-stu';
import { YxtbizPractice } from './practice';
import { YxtbizUlcd } from './ulcd';

import { YxtbizCommonSelector } from './common-selector';
import { YxtbizOpenData } from './open-data';
import { YxtbizUserName } from './user-name';
import { YxtbizDeptName } from './dept-name';
import { YxtbizImportProc } from './import-proc';
import { YxtbizBreadcrumb } from './breadcrumb';
import { YxtbizSelectNewkng } from './select-newkng';
import { YxtbizCollegeSelector } from './college-selector';
import { YxtbizTcmSelect } from './tcm-select';
import { YxtbizNavLang } from './nav-lang';

import { YxtbizI18nLang } from './i18n-lang';
import { YxtbizI18nInput } from './i18n-input';
import { YxtbizI18nText } from './i18n-text';
import { YxtbizCertificatePreview } from './certificate-preview';
import { YxtbizOpenDataDd } from './open-data-dd';
import { YxtbizPositionName } from './position-name';
import { YxtbizDeptManagerTree } from './dept-manager-tree';
import { YxtbizAreaCodeSelect } from './area-code-select';
import { YxtbizQuestionSelector } from './question-selector';
import { YxtbizQuestionPreview } from './question-preview';
import { YxtbizSelectCourse } from './select-course';
import { YxtbizTcmSelectAc } from './tcm-select-ac';
import { YxtbizAiRobot } from './ai-robot';
import { YxtbizMultiClassAudit } from './multi-class-audit';
import { YxtbizCardConsumptAudit } from './card-consumpt-audit';

import { YxtbizKngOperator } from './kng-operator';
import { YxtbizKngPoints } from './kng-points';
import { YxtbizAiProjectAssistant } from './ai-project-assistant';
import { YxtbizUserSelectorPosition } from './user-selector-position';

import { YxtbizDeptTreeV2 } from './dept-tree-v2';
import { YxtbizDeptTreeV3 } from './dept-tree-v3';
import { YxtbizRegisterAudit } from './register-audit';
import { YxtbizProjectAudit } from './project-audit';
import { YxtbizKngScormPlayer } from './kng-scorm-player';
import { YxtbizFlipInfo } from './flip-info';
import { YxtbizRankList } from './rank-list';
import { YxtbizRankSetter } from './rank-setter';
import { YxtbizSupplierSelector } from './supplier-selector';

import { YxtbizProjectPlanSelect } from './project-plan-select';
import { YxtbizHwkTemplateSelector } from './hwk-template-selector';

import { YxtbizO2oEnrollProjectsetAudit } from './o2o-enroll-projectset-audit';

import { YxtbizPracticeSelector } from './practice-selector';
import { YxtbizAcWorkAudit } from './ac-work-audit';
import { YxtbizCheckItemSelector } from './check-item-selector';
import { YxtbizCheckListSelector } from './check-list-selector';
import { YxtbizKngAuditContent } from './kng-audit-content';
import { YxtbizKngCommentAuditContent } from './kng-comment-audit-content';
import { YxtbizRangeSelector } from './range-selector';
import { YxtbizLabelling } from './labelling';
import { YxtbizBoardAudit } from './board-audit';
import { YxtbizCircleAudit } from './circle-audit';
import { YxtbizFaceSelector } from './face-selector';
import { YxtbizMergeEvaluation } from './merge-evaluation';
import { YxtbizO2oMultiEvaluateDialog } from './o2o-multi-evaluate-dialog';
import { YxtbizLanguageSlot } from './language-slot';
import { YxtbizCoursePlayer } from './course-player';
import { YxtbizRank } from './rank';
import { YxtbizSelectRank } from './select-rank';

import { YxtbizAsyncImportList } from './async-import-list';
import { YxtbizShopSelector } from './shop-selector';
import { YxtbizAreaSelector } from './area-selector';
import { YxtbizInspectionSelector } from './inspection-selector';

import { YxtbizNewUlcdIdentify } from './new-ulcd-identify';
import { YxtbizGroupMember } from './group-member';
import { YxtbizTeacherLevel } from './teacher-level';
import { YxtbizLiveSelector } from './live-selector';
import { YxtbizSelectTrainingProjects } from './select-training-projects';
import { YxtbizSelectMaps } from './select-maps';
import { YxtbizEco } from './eco';
import { YxtbizTeacherEnrollAudit } from './teacher-enroll-audit';
import { YxtbizSignup } from './signup';

import { YxtbizSponsorChoose } from './sponsor-choose';
import { YxtbizEnrollManage } from './enroll-manage';
import { YxtbizEnrollDetail } from './enroll-detail';
import { YxtbizO2oEnrollAudit } from './o2o-enroll-audit';
import { YxtbizAcEnrollAudit } from './ac-enroll-audit';
import { YxtbizTeacherSalaryAudit } from './teacher-salary-audit';
import { YxtbizTeacherRdSalaryAudit } from './teacher-rd-salary-audit';
import { YxtbizTeacherEditAudit } from './teacher-edit-audit';
import { YxtbizTeacherSubscribeAudit } from './teacher-subscribe-audit';
import { YxtbizTeacherExperience } from './teacher-experience';
import { YxtbizTeacherSubscribeManage } from './teacher-subscribe-manage';

import { YxtbizNewUlcdCom } from './new-ulcd-com';
import { YxtbizNewUlcdSelect } from './new-ulcd-select';

import { YxtbizProjectSelector } from './project-selector';

import { YxtbizRuleValidity } from './rule-validity';
import { YxtbizRuleCycle } from './rule-cycle';
import { YxtbizRuleCondition } from './rule-condition';
import { YxtbizSurveySelector } from './survey-selector';
import { YxtbizTypeSearch } from './type-search';
import { YxtbizIntelligentQuestioning } from './intelligent-questioning';

import { YxtbizSelectKngCatalogV2 } from './select-kng-catalog-v2';

import { YxtbizAbilityPreview } from './ability-preview';
import { YxtbizSkillViewer } from './skill-viewer';
import { YxtbizSkillDetails } from './skill-details';
import { YxtbizPolestarRadar } from './polestar-radar';
import { YxtbizTrainingsStandard } from './trainings-standard';
import { YxtbizModelSelector } from './model-selector';
import { YxtbizEvalDelay } from './eval-delay';
import { YxtbizEvalCreateDrawer } from './eval-create-drawer';
import { YxtbizEvalEvaluatorTable } from './eval-evaluator-table';
import { YxtbizEvalUserManagement } from './eval-user-management';
import { YxtbizEvalImportEvalCreate } from './eval-import-eval-create';
import { YxtbizEvalTrainingDialog } from './eval-training-dialog';
import { YxtbizSupplierSelect } from './supplier-select';
import { YxtbizExtendFieldSelector } from './extend-field-selector';
import { YxtbizMengniuAnnualAudit } from './mengniu-annual-audit';
import { YxtbizMengniuMonthAudit } from './mengniu-month-audit';
import { YxtbizLibSelector } from './lib-selector';
import { YxtbizHourRecordAudit } from './hour-record-audit';
import { YxtbizSelectTeam } from './select-team';
import { YxtbizSparringProjectSelector } from './sparring-project-selector';

import { YxtbizSelectDimension } from './select-dimension';
import { YxtbizDimension } from './dimension';
import { YxtbizLabelAudit } from './label-audit';
import { YxtbizArrangeStatistics } from './arrange-statistics';
import { YxtbizOpenDownloadTool } from './open-download-tool';
import { YxtbizKngScormCompleteStandard } from './kng-scorm-complete-standard';
import { YxtbizAuditWorkflowDetail } from './audit-workflow-detail';
import { YxtbizDocPlayer } from './doc-player';
import { YxtbizArrangeSelectorPaas } from './arrange-selector-paas';
import { YxtbizPracticeSelectorPaas } from './practice-selector-paas';
import { YxtbizIntelligentQuestioningTcmSources } from './intelligent-questioning-tcm-sources'
import { YxtbizTcmPracticeDetail } from './tcm-practice-detail'
import { YxtbizTcmPracticeView } from './tcm-practice-view'

import { YxtbizAibox } from './aibox';
import { YxtbizAiboxUnify } from './aibox-unify';

import { YxtbizTmapAudit } from './tmap-audit';
import { YxtbizTeacherSelectorV2 } from './teacher-selector-v2';
import { YxtbizTutorSelectorV2 } from './tutor-selector-v2';
import { YxtbizDiscussSpeech } from './discuss-speech';
import { YxtbizDiscussQuestion } from './discuss-question';
import { YxtbizAttachmentList } from './attachment-list';
import { YxtbizAiTrainingDesign } from './ai-training-design';
import { YxtbizFlipLeaveAudit } from './flip-leave-audit';
import { YxtbizAttendLeaveAudit } from './attend-leave-audit';
import { YxtbizArrangeAudit } from './arrange-audit';
import { YxtbizAiAssistant } from './ai-assistant';
import { YxtbizFlowEditor } from './flow-editor';
import { YxtbizMarkdownEditor } from './markdown-editor';
import { YxtbizImageCropperv2 } from './image-cropperv2';
import { YxtbizDutySelector } from './duty-selector';
import { YxtbizSelectAbilityDuty } from './select-ability-duty';
import { YxtbizDeclarationHour } from './declaration-hour';
import { YxtbizIndicatorDetail } from './indicator-detail';
import { YxtbizSelectQuota } from './select-quota';
import { YxtbizSelectModel } from './select-model';
import { YxtbizTalentStandard } from './talent-standard';
import { YxtbizEvalTrack } from './eval-track';
import { YxtbizArrangeTrack } from './arrange-track';
import { YxtbizEvalRelation } from './eval-relation';
import { YxtbizCreateProject } from './create-project';
import { YxtbizAiHwReview } from './ai-hw-review';
import { YxtbizRptSelectKng } from './rpt-select-kng';
import { YxtbizStuNavControl } from './stu-nav-control';
import { YxtbizDingMsg } from './ding-msg';
import { YxtbizRptSelectArea } from './rpt-select-area';
import { YxtbizRptSelectCheckItem } from './rpt-select-check-item';
import { YxtbizRptSelectCheckList } from './rpt-select-check-list';
import { YxtbizRptSelectInspection } from './rpt-select-inspection';
import { YxtbizRptSelectShop } from './rpt-select-shop';
import { YxtbizTalentAbilityTrend } from './talent-ability-trend';
import { YxtbizTalentEvalTrend } from './talent-eval-trend';

import { YxtbizActivityOutline } from './activity-outline';

import { YxtbizUacdTaskList } from './uacd-task-list';
import { YxtbizUacdStore } from './uacd-store';
import { YxtbizUacd } from './uacd';
import { YxtbizDimensionRule } from './dimension-rule';
import { YxtbizTalentModel } from './talent-model';
import { YxtbizTalentQuota } from './talent-quota';
import { YxtbizAomProjectAudit } from './aom-project-audit';
import { YxtbizTalentAbilityTrendReport } from './talent-ability-trend-report';
import { YxtbizTalentEvalTrendReport } from './talent-eval-trend-report';
import { YxtbizTalentTrainingsStandard } from './talent-trainings-standard';

import { YxtbizCapacityRankingList } from './capacity-ranking-list';
import { YxtbizRelevancePlan } from './relevance-plan';
import { YxtbizTalentCalibration } from './talent-calibration';
import { YxtbizTalentReportSelectProjects } from './talent-report-select-projects';
import { YxtbizTalentSelectMap } from './talent-select-map';
import { YxtbizPerformanceForSignup } from './performance-for-signup';
import { YxtbizRptSelectProject } from './rpt-select-project';
import { YxtbizRptSelectPlan } from './rpt-select-plan';
import { YxtbizIpaasTriggerEvent } from './ipaas-trigger-event';
import { YxtbizIpaasExecuteAction } from './ipaas-execute-action';
import { YxtbizRptSelectLive } from './rpt-select-live'
import { YxtbizTagSelector } from './tag-selector'
import { YxtbizStudyMapDesignList } from './study-map-design-list';
import { YxtbizAiAnswerSetting } from './ai-answer-setting'
import { YxtbizAiEvaluationReport } from './ai-evaluation-report'
import { YxtbizNioClassAudit } from './nio-class-audit'
import { YxtbizTeamSelector } from './team-selector'

export class Header extends YxtbizHeader {}

/** Leftmenu Component */
export class Leftmenu extends YxtbizLeftmenu {}

/** Footer Component */
export class Footer extends YxtbizFooter {}

/** NavTop Component */
export class NavTop extends YxtbizNavTop {}

/** Api Component */
export class Api extends YxtbizApi {}

/** DeptTree Component */
export class DeptTree extends YxtbizDeptTree {}

/** NavLeft Component */
export class NavLeft extends YxtbizNavLeft {}

/** NavMain Component */
export class NavMain extends YxtbizNavMain {}

/** PosTree Component */
export class PosTree extends YxtbizPosTree {}

/** UserGroupTree Component */
export class UserGroupTree extends YxtbizUserGroupTree {}

/** CheckPersonRange Component */
export class CheckPersonRange extends YxtbizCheckPersonRange {}

/** CheckSinglePosition Component */
export class CheckSinglePosition extends YxtbizCheckSinglePosition {}

/** FunsTree Component */
export class FunsTree extends YxtbizFunsTree {}

/** Upload Component */
export class Upload extends YxtbizUpload {}

/** SurveyTemplate Component */
export class SurveyTemplate extends YxtbizSurveyTemplate {}

/** ExamSelector Component */
export class ExamSelector extends YxtbizExamSelector {}

/** ExamArrange Component */
export class ExamArrange extends YxtbizExamArrange {}

/** ExamArrangeSelector Component */
export class ExamArrangeSelector extends YxtbizExamArrangeSelector {}

/** TeacherSelector Component */
export class TeacherSelector extends YxtbizTeacherSelector {}

/** TutorSelector Component */
export class TutorSelector extends YxtbizTutorSelector {}

/** CertificateSelector Component */
export class CertificateSelector extends YxtbizCertificateSelector {}

/** Qrcode Component */
export class Qrcode extends YxtbizQrcode {}

/** Complain Component */
export class Complain extends YxtbizComplain {}

/** ImageCropper Component */
export class ImageCropper extends YxtbizImageCropper {}

/** AreaSelect Component */
export class AreaSelect extends YxtbizAreaSelect {}

/** Richeditor Component */
export class Richeditor extends YxtbizRicheditor {}
/** ImageViewer Component */
export class ImageViewer extends YxtbizImageViewer {}

/** PlaceSelector Component */
export class PlaceSelector extends YxtbizPlaceSelector {}

/** Amap ComAmapt */
export class Amap extends YxtbizAmap {}
export class Personalitytemplate extends YxtbizPersonalitytemplate {}

/** EnrollSettings Component */
export class EnrollSettings extends YxtbizEnrollSettings {}

/** Reward Component */
export class Reward extends YxtbizReward {}

/** SelectKng ComAmapt */
export class SelectKng extends YxtbizSelectKng {}

/** Watermark ComAmapt */
export class Watermark extends YxtbizWatermark {}
/** ExamArrangeMakeup Component */
export class ExamArrangeMakeup extends YxtbizExamArrangeMakeup {}

/** DocViewer Component */
export class DocViewer extends YxtbizDocViewer {}

/** Comment Component */
export class Comment extends YxtbizComment {}

/** NavTopStu Component */
export class NavTopStu extends YxtbizNavTopStu {}

/** NavFooter Component */
export class NavFooter extends YxtbizNavFooter {}

/** SelectKngCatalog Component */
export class SelectKngCatalog extends YxtbizSelectKngCatalog {}

/** SelectKngCatalog Component */
export class SelectKngCatelogSource extends YxtbizSelectKngCatelogSource {}

/** SelectKngSource Component */
export class SelectKngSource extends YxtbizSelectKngSource {}

/** InfoSelector Component */
export class InfoSelector extends YxtbizInfoSelector {}

/** Video Component */
export class Video extends YxtbizVideo {}

/** Ulcd Component */
export class Ulcd extends YxtbizUlcd {}

/** NavBreadcrumb Component */
export class NavBreadcrumb extends YxtbizNavBreadcrumb {}

/** PersonRangeSelector Component */
export class PersonRangeSelector extends YxtbizPersonRangeSelector {}

/** UploadImage Component */
export class UploadImage extends YxtbizUploadImage {}

/** Voice Component */
export class Voice extends YxtbizVoice {}

/** PersonalCenterNav Component */
export class PersonalCenterNav extends YxtbizPersonalCenterNav {}

/** Search Component */
export class Search extends YxtbizSearch {}

/** NavImmersive Component */
export class NavImmersive extends YxtbizNavImmersive {}

/** NavTab Component */
export class NavTab extends YxtbizNavTab {}

/** SupportSidebar Component */
export class SupportSidebar extends YxtbizSupportSidebar {}

/** VirtualList Component */
export class VirtualList extends YxtbizVirtualList {}

/** UserCenterNav Component */
export class UserCenterNav extends YxtbizUserCenterNav {}

/** NavLeftStu Component */
export class NavLeftStu extends YxtbizNavLeftStu {}

/** Practice Component */
export class Practice extends YxtbizPractice {}

/** CommonSelector Component */
export class CommonSelector extends YxtbizCommonSelector {}

/** OpenData Component */
export class OpenData extends YxtbizOpenData {}

/** UserName Component */
export class UserName extends YxtbizUserName {}

/** DeptName Component */
export class DeptName extends YxtbizDeptName {}

/** ImportProc Component */
export class ImportProc extends YxtbizImportProc {}

/** Breadcrumb Component */
export class Breadcrumb extends YxtbizBreadcrumb {}

/** SelectNewkng Component */
export class SelectNewkng extends YxtbizSelectNewkng {}

/** CollegeSelector Component */
export class CollegeSelector extends YxtbizCollegeSelector {}

/** NavLang Component */
export class NavLang extends YxtbizNavLang {}

/** I18nLang Component */
export class I18nLang extends YxtbizI18nLang {}

/** I18nInput Component */
export class I18nInput extends YxtbizI18nInput {}

/** TcmSelect Component */
export class TcmSelect extends YxtbizTcmSelect {}

/** I18nText Component */
export class I18nText extends YxtbizI18nText {}

/** CertificatePreview Component */
export class CertificatePreview extends YxtbizCertificatePreview {}

/** OpenDataDd Component */
export class OpenDataDd extends YxtbizOpenDataDd {}

/** PositionName Component */
export class PositionName extends YxtbizPositionName {}

/** DeptManagerTree Component */
export class DeptManagerTree extends YxtbizDeptManagerTree {}

/** AreaCodeSelect Component */
export class AreaCodeSelect extends YxtbizAreaCodeSelect {}

export class QuestionSelector extends YxtbizQuestionSelector {}
export class QuestionPreview extends YxtbizQuestionPreview {}

/** SelectCourse Component */
export class SelectCourse extends YxtbizSelectCourse {}

/** SponsorChoose Component */
export class SponsorChoose extends YxtbizSponsorChoose {}
/** EnrollManage Component */
export class EnrollManage extends YxtbizEnrollManage {}

/** EnrollDetail Component */
export class EnrollDetail extends YxtbizEnrollDetail {}

/** TeacherLevel Component */
export class TeacherLevel extends YxtbizTeacherLevel {}

/** LiveSelector Component */
export class LiveSelector extends YxtbizLiveSelector {}

/** Eco Component */
export class Eco extends YxtbizEco {}

/** TeacherEnrollAudit Component */
export class TeacherEnrollAudit extends YxtbizTeacherEnrollAudit {}

/** CoursePlayer Component */
export class CoursePlayer extends YxtbizCoursePlayer {}

/** Signup Component */
export class Signup extends YxtbizSignup {}

/** NewUlcdCom Component */
export class NewUlcdCom extends YxtbizNewUlcdCom {}

/** NewUlcdSelect Component */
export class NewUlcdSelect extends YxtbizNewUlcdSelect {}
/** SelectTrainingProjects Component */
export class SelectTrainingProjects extends YxtbizSelectTrainingProjects {}

/** SelectMaps Component */
export class SelectMaps extends YxtbizSelectMaps {}

/** AsyncImportList Component */
export class AsyncImportList extends YxtbizAsyncImportList {}

/** ShopSelector Component */
export class ShopSelector extends YxtbizShopSelector {}
/** AreaSelector Component */
export class AreaSelector extends YxtbizAreaSelector {}

/** InspectionSelector Component */
export class InspectionSelector extends YxtbizInspectionSelector {}
/** NewUlcdIdentify Component */
export class NewUlcdIdentify extends YxtbizNewUlcdIdentify {}

/** GroupMember Component */
export class GroupMember extends YxtbizGroupMember {}
/** LanguageSlot Component */
export class LanguageSlot extends YxtbizLanguageSlot {}

/** O2oEnrollAudit Component */
export class O2oEnrollAudit extends YxtbizO2oEnrollAudit {}

/** ProjectSelector Component */
export class ProjectSelector extends YxtbizProjectSelector {}
/** MergeEvaluation Component */
export class MergeEvaluation extends YxtbizMergeEvaluation {}
/** O2oMultiEvaluateDialog Component */
export class O2oMultiEvaluateDialog extends YxtbizO2oMultiEvaluateDialog {}
/** Rank Component */
export class Rank extends YxtbizRank {}

/** SelectRank Component */
export class SelectRank extends YxtbizSelectRank {}

/** RuleCondition Component */
export class RuleCondition extends YxtbizRuleCondition {}

/** RuleCycle Component */
export class RuleCycle extends YxtbizRuleCycle {}

/** RuleValidity Component */
export class RuleValidity extends YxtbizRuleValidity {}

/** AcEnrollAudit Component */
export class AcEnrollAudit extends YxtbizAcEnrollAudit {}
/** Labelling Component */
export class Labelling extends YxtbizLabelling {}
/** SurveySelector Component */
export class SurveySelector extends YxtbizSurveySelector {}

/** TeacherSalaryAudit Component */
export class TeacherSalaryAudit extends YxtbizTeacherSalaryAudit {}

/** TeacherRdSalaryAudit Component */
export class TeacherRdSalaryAudit extends YxtbizTeacherRdSalaryAudit {}

/** TeacherEditAudit Component */
export class TeacherEditAudit extends YxtbizTeacherEditAudit {}

/** TeacherSubscribeAudit Component */
export class TeacherSubscribeAudit extends YxtbizTeacherSubscribeAudit {}

/** TeacherExperience Component */
export class TeacherExperience extends YxtbizTeacherExperience {}

/** TeacherSubscribeManage Component */
export class TeacherSubscribeManage extends YxtbizTeacherSubscribeManage {}

/** RangeSelector Component */
export class RangeSelector extends YxtbizRangeSelector {}

/** BoardAudit Component */
export class BoardAudit extends YxtbizBoardAudit {}

/** CircleAudit Component */
export class CircleAudit extends YxtbizCircleAudit {}
/** FaceSelector Component */
export class FaceSelector extends YxtbizFaceSelector {}

/** RankList Component */
export class RankList extends YxtbizRankList {}

/** RankSetter Component */
export class RankSetter extends YxtbizRankSetter {}
/** PracticeSelector Component */
export class PracticeSelector extends YxtbizPracticeSelector {}

/** CheckItemSelector Component */
export class CheckItemSelector extends YxtbizCheckItemSelector {}

/** AcWorkAudit Component */
export class AcWorkAudit extends YxtbizAcWorkAudit {}

/** CheckListSelector Component */
export class CheckListSelector extends YxtbizCheckListSelector {}
/** KngAuditContent Component */
export class KngAuditContent extends YxtbizKngAuditContent {}

/** KngCommentAuditContent Component */
export class KngCommentAuditContent extends YxtbizKngCommentAuditContent {}

/** O2oEnrollProjectsetAudit Component */
export class O2oEnrollProjectsetAudit extends YxtbizO2oEnrollProjectsetAudit {}

/** ProjectPlanSelect Component */
export class ProjectPlanSelect extends YxtbizProjectPlanSelect {}

/** HwkTemplateSelector Component */
export class HwkTemplateSelector extends YxtbizHwkTemplateSelector {}

/** KngScormPlayer Component */
export class KngScormPlayer extends YxtbizKngScormPlayer {}
/** TypeSearch Component */

export class TypeSearch extends YxtbizTypeSearch {}

/** FlipInfo Component */
export class FlipInfo extends YxtbizFlipInfo {}

/** RegisterAudit Component */
export class RegisterAudit extends YxtbizRegisterAudit {}
/** ProjectAudit Component */
export class ProjectAudit extends YxtbizProjectAudit {}

/** TcmSelectAc Component */
export class TcmSelectAc extends YxtbizTcmSelectAc {}

/** AiRobot Component */
export class AiRobot extends YxtbizAiRobot {}
/** MultiClassAudit Component */
export class MultiClassAudit extends YxtbizMultiClassAudit {}
/** CardConsumptAudit Component */
export class CardConsumptAudit extends YxtbizCardConsumptAudit {}
/** KngOperator Component */
export class KngOperator extends YxtbizKngOperator {}

/** KngPoints Component */
export class KngPoints extends YxtbizKngPoints {}
/** AiProjectAssistant Component */
export class AiProjectAssistant extends YxtbizAiProjectAssistant {}
/** SupplierSelector Component */
export class SupplierSelector extends YxtbizSupplierSelector {}

/** SupplierSelect Component */
export class SupplierSelect extends YxtbizSupplierSelect {}

/** IntelligentQuestioning Component */
export class IntelligentQuestioning extends YxtbizIntelligentQuestioning {}

/** SelectKngCatalogV2 Component */
export class SelectKngCatalogV2 extends YxtbizSelectKngCatalogV2 {}

/** AbilityPreview Component */
export class AbilityPreview extends YxtbizAbilityPreview {}

export class SkillViewer extends YxtbizSkillViewer {}

/** SkillDetails Component */
export class SkillDetails extends YxtbizSkillDetails {}

/** PolestarRadar Component */
export class PolestarRadar extends YxtbizPolestarRadar {}

/** TrainingsStandard Component */
export class TrainingsStandard extends YxtbizTrainingsStandard {}

/** ModelSelector Component */
export class ModelSelector extends YxtbizModelSelector {}

/** EvalDelay Component */
export class EvalDelay extends YxtbizEvalDelay {}
export class EvalCreateDrawer extends YxtbizEvalCreateDrawer {}

/** EvalEvaluatorTable Component */
export class EvalEvaluatorTable extends YxtbizEvalEvaluatorTable {}

/** EvalUserManagement Component */
export class EvalUserManagement extends YxtbizEvalUserManagement {}

/** EvalImportEvalCreate Component */
export class EvalImportEvalCreate extends YxtbizEvalImportEvalCreate {}

/** EvalTrainingDialog Component */
export class EvalTrainingDialog extends YxtbizEvalTrainingDialog {}
/** DeptTreeV2 Component */
export class DeptTreeV2 extends YxtbizDeptTreeV2 {}

/** DeptTreeV3 Component */
export class DeptTreeV3 extends YxtbizDeptTreeV3 {}

/** UserSelectorPosition Component */
export class UserSelectorPosition extends YxtbizUserSelectorPosition {}

/** ExtendFieldSelector Component */
export class ExtendFieldSelector extends YxtbizExtendFieldSelector {}

/** MengniuAnnualAudit Component */
export class MengniuAnnualAudit extends YxtbizMengniuAnnualAudit {}

/** MengniuMonthAudit Component */
export class MengniuMonthAudit extends YxtbizMengniuMonthAudit {}
/** LibSelector Component */
export class LibSelector extends YxtbizLibSelector {}
/** HourRecordAudit Component */
export class HourRecordAudit extends YxtbizHourRecordAudit {}

/** SelectTeam Component */
export class SelectTeam extends YxtbizSelectTeam {}

/** SelectDimension Component */
export class SelectDimension extends YxtbizSelectDimension {}

/** SparringProjectSelector Component */
export class SparringProjectSelector extends YxtbizSparringProjectSelector {}

/** Dimension Component */
export class Dimension extends YxtbizDimension {}

/** LabelAudit Component */
export class LabelAudit extends YxtbizLabelAudit {}
/** ArrangeStatistics Component */
export class ArrangeStatistics extends YxtbizArrangeStatistics {}
/** OpenDownloadTool Component */
export class OpenDownloadTool extends YxtbizOpenDownloadTool {}

/** KngScormCompleteStandard Component */
export class KngScormCompleteStandard extends YxtbizKngScormCompleteStandard {}

/** AuditWorkflowDetail Component */
export class AuditWorkflowDetail extends YxtbizAuditWorkflowDetail {}
/** Aibox Component */
export class Aibox extends YxtbizAibox {}
/** TmapAudit Component */
export class TmapAudit extends YxtbizTmapAudit {}

/** AiboxUnify Component */
export class AiboxUnify extends YxtbizAiboxUnify {}

/** DocPlayer Component */
export class DocPlayer extends YxtbizDocPlayer {}

/** TeacherSelectorV2 Component */
export class TeacherSelectorV2 extends YxtbizTeacherSelectorV2 {}

/** TutorSelectorV2 Component */
export class TutorSelectorV2 extends YxtbizTutorSelectorV2 {}

/** DiscussionSpeech Component */
export class DiscussSpeech extends YxtbizDiscussSpeech {}

/** DiscussionQuestion Component */
export class DiscussQuestion extends YxtbizDiscussQuestion {}

/** AttachmentList Component */
export class AttachmentList extends YxtbizAttachmentList {}

/** AiTrainingDesign Component */
export class AiTrainingDesign extends YxtbizAiTrainingDesign {}

/** FlipLeaveAudit Component */
export class FlipLeaveAudit extends YxtbizFlipLeaveAudit {}

/** AttendLeaveAudit Component */
export class AttendLeaveAudit extends YxtbizAttendLeaveAudit {}

/** ArrangeAudit Component */
export class ArrangeAudit extends YxtbizArrangeAudit {}
/** AiAssistant Component */
export class AiAssistant extends YxtbizAiAssistant {}

/** FlowEditor Component */
export class FlowEditor extends YxtbizFlowEditor {}
/** MarkdownEditor Component */
export class MarkdownEditor extends YxtbizMarkdownEditor {}

/** ImageCropperv2 Component */
export class ImageCropperv2 extends YxtbizImageCropperv2 {}

/** DutySelector Component */
export class DutySelector extends YxtbizDutySelector {}

/** SelectAbilityDuty Component */
export class SelectAbilityDuty extends YxtbizSelectAbilityDuty {}
/** DeclarationHour Component */
export class DeclarationHour extends YxtbizDeclarationHour {}

/** IndicatorDetail Component */
export class IndicatorDetail extends YxtbizIndicatorDetail {}

/** SelectQuota Component */
export class SelectQuota extends YxtbizSelectQuota {}

/** SelectModel Component */
export class SelectModel extends YxtbizSelectModel {}

/** TalentStandard Component */
export class TalentStandard extends YxtbizTalentStandard {}

/** EvalTrack Component */
export class EvalTrack extends YxtbizEvalTrack {}

/** ArrangeTrack Component */
export class ArrangeTrack extends YxtbizArrangeTrack {}
/** EvalRelation Component */
export class EvalRelation extends YxtbizEvalRelation {}
/** CreateProject Component */
export class CreateProject extends YxtbizCreateProject {}
/** ActivityOutline Component */
export class ActivityOutline extends YxtbizActivityOutline {}
/** Uacd Component */
export class Uacd extends YxtbizUacd {}
/** UacdStore Component */
export class UacdStore extends YxtbizUacdStore {}
/** UacdTaskList Component */
export class UacdTaskList extends YxtbizUacdTaskList {}

export class DimensionRule extends YxtbizDimensionRule {}

/** TalentModel Component */
export class TalentModel extends YxtbizTalentModel {}

/** TalentQuota Component */
export class TalentQuota extends YxtbizTalentQuota {}

/** AomProjectAudit Component */
export class AomProjectAudit extends YxtbizAomProjectAudit {}

/** TalentAbilityTrend Component */
export class TalentAbilityTrend extends YxtbizTalentAbilityTrend {}

/** TalentEvalTrend Component */
export class TalentEvalTrend extends YxtbizTalentEvalTrend {}

/** TalentAbilityTrendReport Component */
export class TalentAbilityTrendReport extends YxtbizTalentAbilityTrendReport {}

/** TalentEvalTrendReport Component */
export class TalentEvalTrendReport extends YxtbizTalentEvalTrendReport {}
/** TalentTrainingsStandard Component */
export class TalentTrainingsStandard extends YxtbizTalentTrainingsStandard {}

/** CapacityRankingList Component */
export class CapacityRankingList extends YxtbizCapacityRankingList {}

export class RelevancePlan extends YxtbizRelevancePlan {}

/** RptSelectPlan Component */
export class AiHwReview extends YxtbizAiHwReview {}

/** RptSelectKng Component */
export class RptSelectKng extends YxtbizRptSelectKng {}
/** PerformanceForSignup Component */
export class PerformanceForSignup extends YxtbizPerformanceForSignup {}

/** RptSelectProject Component */
export class RptSelectProject extends YxtbizRptSelectProject {}

/** RptSelectPlan Component */
export class RptSelectPlan extends YxtbizRptSelectPlan {}

/** TalentCalibration Component */
export class TalentCalibration extends YxtbizTalentCalibration {}

/** TalentReportSelectProjects Component */
export class TalentReportSelectProjects extends YxtbizTalentReportSelectProjects {}

/** TalentSelectMap Component */
export class TalentSelectMap extends YxtbizTalentSelectMap {}


/** IpaasTriggerEvent Component */
export class IpaasTriggerEvent extends YxtbizIpaasTriggerEvent {}

/** IpaasExecuteAction Component */
export class IpaasExecuteAction extends YxtbizIpaasExecuteAction {}

/** StuNavControl Component */
export class StuNavControl extends YxtbizStuNavControl {}

/** DingMsg Component */
export class DingMsg extends YxtbizDingMsg {}


/** RptSelectArea Component */
export class RptSelectArea extends YxtbizRptSelectArea {}

/** RptSelectCheckItem Component */
export class RptSelectCheckItem extends YxtbizRptSelectCheckItem {}

/** RptSelectCheckList Component */
export class RptSelectCheckList extends YxtbizRptSelectCheckList {}

/** RptSelectInspection Component */
export class RptSelectInspection extends YxtbizRptSelectInspection {}

/** RptSelectShop Component */
export class RptSelectShop extends YxtbizRptSelectShop {}

/** ArrangeSelectorPaas Component */
export class ArrangeSelectorPaas extends YxtbizArrangeSelectorPaas {}

/** PracticeSelectorPaas Component */
export class PracticeSelectorPaas extends YxtbizPracticeSelectorPaas {}

/** TagSelector Component */
export class TagSelector extends YxtbizTagSelector {}

/** RptSelectLive Component */
export class RptSelectLive extends YxtbizRptSelectLive {}

/** AiAnswerSetting Component */
export class AiAnswerSetting extends YxtbizAiAnswerSetting {}

/** AiEvaluationReport Component */
export class AiEvaluationReport extends YxtbizAiEvaluationReport {}

/** StudyMapDesignList Component */
export class StudyMapDesignList extends YxtbizStudyMapDesignList {}

/** IntelligentQuestioningTcmSources Component */
export class IntelligentQuestioningTcmSources extends YxtbizIntelligentQuestioningTcmSources {}

/** TcmPracticeDetail Component */
export class TcmPracticeDetail extends YxtbizTcmPracticeDetail {}

/** TcmPracticeView Component */
export class TcmPracticeView extends YxtbizTcmPracticeView {}

/** NioClassAudit Component */
export class NioClassAudit extends YxtbizNioClassAudit {}
/** TeamSelector Component */
export class TeamSelector extends YxtbizTeamSelector {}
